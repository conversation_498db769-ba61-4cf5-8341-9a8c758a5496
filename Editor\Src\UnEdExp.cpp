/*=============================================================================
	UnEdExp.cpp: Editor exporters.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.
=============================================================================*/

#include "EditorPrivate.h"

/*------------------------------------------------------------------------------
	UTextBufferExporterTXT implementation.
------------------------------------------------------------------------------*/

void UTextBufferExporterTXT::StaticConstructor()
{
	guard(UTextBufferExporterTXT::StaticConstructor);

	SupportedClass = UTextBuffer::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("TXT"));

	unguard;
}
UBOOL UTextBufferExporterTXT::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(UTextBufferExporterTXT::ExportText);
	UTextBuffer* TextBuffer = CastChecked<UTextBuffer>( Object );
	FString Str( TextBuffer->Text );

	TCHAR* Start = const_cast<TCHAR*>(*Str);
	TCHAR* End   = Start + Str.Len();
	while( Start<End && (Start[0]=='\r' || Start[0]=='\n' || Start[0]==' ') )
		Start++;
	while( End>Start && (End [-1]=='\r' || End [-1]=='\n' || End [-1]==' ') )
		End--;
	*End = 0;

	Ar.Log( Start );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(UTextBufferExporterTXT);

/*------------------------------------------------------------------------------
	USoundExporterWAV implementation.
------------------------------------------------------------------------------*/

void USoundExporterWAV::StaticConstructor()
{
	guard(USoundExporterWAV::StaticConstructor);

	SupportedClass = USound::StaticClass();
	bText = 0;
	new(Formats)FString(TEXT("WAV"));

	unguard;
}
UBOOL USoundExporterWAV::ExportBinary( UObject* Object, const TCHAR* Type, FArchive& Ar, FFeedbackContext* Warn )
{
	guard(USoundExporterWAV::ExportBinary);
	USound* Sound = CastChecked<USound>( Object );
	Sound->Data.Load();
	Ar.Serialize( &Sound->Data(0), Sound->Data.Num() );
	return 1;
	unguard;
}
IMPLEMENT_CLASS(USoundExporterWAV);

/*------------------------------------------------------------------------------
	UMusicExporterTracker implementation.
------------------------------------------------------------------------------*/

void UMusicExporterTracker::StaticConstructor()
{
	guard(UMusicExporterTracker::StaticConstructor);

	SupportedClass = UMusic::StaticClass();
	bText = 0;
	new(Formats)FString(TEXT("*"));

	unguard;
}
UBOOL UMusicExporterTracker::ExportBinary( UObject* Object, const TCHAR* Type, FArchive& Ar, FFeedbackContext* Warn )
{
	guard(UMusicExporterTracker::ExportBinary);
	UMusic* Music = CastChecked<UMusic>( Object );
	Music->Data.Load();
	Ar.Serialize( &Music->Data(0), Music->Data.Num() );
	return 1;
	unguard;
}
IMPLEMENT_CLASS(UMusicExporterTracker);

/*------------------------------------------------------------------------------
	UClassExporterH implementation.
------------------------------------------------------------------------------*/

static void RecursiveTagNames( UClass* Class )
{
	guard(RecursiveTagNames);
	if( (Class->GetFlags() & RF_TagExp) && (Class->GetFlags() & RF_Native) )
		for( TFieldIterator<UFunction> Function(Class); Function && Function.GetStruct()==Class; ++Function )
			if
			(	(Function->FunctionFlags & FUNC_Event)
			&&	!Function->GetSuperFunction() )
				Function->GetFName().SetFlags( RF_TagExp );
	for( TObjectIterator<UClass> It; It; ++It )
		if( It->GetSuperClass()==Class )
			RecursiveTagNames( *It );
	unguard;
}

void UClassExporterH::StaticConstructor()
{
	guard(UClassExporterH::StaticConstructor);

	SupportedClass = UClass::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("H"));

	unguard;
}
UBOOL UClassExporterH::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(UClassExporterH::ExportText);
	UClass* Class = CastChecked<UClass>( Object );

	TCHAR API[256];
	appStrcpy( API, Class->GetOuter()->GetName() );
	appStrupr( API );

	// Export as C++ header.
	if( RecursionDepth==0 )
	{
		DidTop = 0;
		RecursiveTagNames( Class );
	}

	// Export this.
	if( Class->GetFlags() & RF_TagExp )
	{
		// Top of file.
		if( !DidTop )
		{
			DidTop = 1;
			Ar.Logf
			(
				TEXT("/*===========================================================================\r\n")
				TEXT("    C++ class definitions exported from UnrealScript.\r\n")
				TEXT("    This is automatically generated by the tools.\r\n")
				TEXT("    DO NOT modify this manually! Edit the corresponding .uc files instead!\r\n")
				TEXT("===========================================================================*/\r\n")
				TEXT("#if SUPPORTS_PRAGMA_PACK\r\n")
				TEXT("#pragma pack (push,%i)\r\n")
				TEXT("#endif\r\n")
				TEXT("\r\n")
				TEXT("#ifndef %s_API\r\n")
				TEXT("#define %s_API DLL_IMPORT\r\n")
				TEXT("#endif\r\n")
				TEXT("\r\n")
				TEXT("#ifndef NAMES_ONLY\r\n")
				TEXT("#define AUTOGENERATE_NAME(name) extern %s_API FName %s_##name;\r\n")
				TEXT("#define AUTOGENERATE_FUNCTION(cls,idx,name)\r\n")
				TEXT("#endif\r\n")
				TEXT("\r\n"),
				OBJECT_ALIGNMENT,
				API,
				API,
				API,
				API
			);
			for( INT i=0; i<FName::GetMaxNames(); i++ )
				if( FName::GetEntry(i) && (FName::GetEntry(i)->Flags & RF_TagExp) )
					Ar.Logf( TEXT("AUTOGENERATE_NAME(%s)\r\n"), *FName((EName)(i)) );
			for( INT i=0; i<FName::GetMaxNames(); i++ )
				if( FName::GetEntry(i) )
					FName::GetEntry(i)->Flags &= ~RF_TagExp;
			Ar.Logf( TEXT("\r\n#ifndef NAMES_ONLY\r\n\r\n") );
		}

		// Enum definitions.
		for( TFieldIterator<UEnum> ItE(Class); ItE && ItE.GetStruct()==Class; ++ItE )
		{
			// Export enum.
			if( ItE->GetOuter()==Class )
			{
				Ar.Logf( TEXT("%senum %s\r\n{\r\n"), appSpc(TextIndent), ItE->GetName() );
				INT i;
				for( i=0; i<ItE->Names.Num(); i++ )
					Ar.Logf( TEXT("%s    %-24s=%i,\r\n"), appSpc(TextIndent), *ItE->Names(i), i );
				if( appStrchr(*ItE->Names(0),'_') )
				{
					// Include tag_MAX enumeration.
					TCHAR Temp[256];
					appStrcpy( Temp, *ItE->Names(0) );
					appStrcpy( appStrchr(Temp,'_'),TEXT("_MAX"));
					Ar.Logf( TEXT("%s    %-24s=%i,\r\n"), appSpc(TextIndent), Temp, i );
				}
				Ar.Logf( TEXT("};\r\n") );
			}
			else Ar.Logf( TEXT("%senum %s;\r\n"), appSpc(TextIndent), ItE->GetName() );
		}

		// Struct definitions.
		for( TFieldIterator<UStruct> ItS(Class); ItS && ItS.GetStruct()==Class; ++ItS )
		{
			if( ItS->GetFlags() & RF_Native )
			{
				// Export struct.
				Ar.Logf( TEXT("struct %s_API %s"), API, ItS->GetNameCPP() );
				if( ItS->SuperField )
					Ar.Logf(TEXT(" : public %s\r\n"), ItS->GetSuperStruct()->GetNameCPP() );
				Ar.Logf( TEXT("\r\n{\r\n") );
				for( TFieldIterator<UProperty> It2(*ItS); It2; ++It2 )
				{
					if( It2.GetStruct()==*ItS )
					{
						Ar.Logf( appSpc(TextIndent+4) );
						It2->ExportCpp( Ar, 0, 0 );
						Ar.Logf(TEXT(";\r\n"));
					}
				}
				Ar.Logf( TEXT("};\r\n\r\n") );
			}
		}

		// Constants.
		for( TFieldIterator<UConst> ItC(Class); ItC && ItC.GetStruct()==Class; ++ItC )
		{
			FString V = ItC->Value;
			while( V.Left(1)==TEXT(" ") )
				V=V.Mid(1);
			if( V.Len()>1 && V.Left(1)==TEXT("'") && V.Right(1)==TEXT("'") )
				V = V.Mid(1,V.Len()-2);
			Ar.Logf( TEXT("#define UCONST_%s %s\r\n"), ItC->GetName(), *V );
		}
		if( TFieldIterator<UConst>(Class) )
			Ar.Logf( TEXT("\r\n") );

		// Parms struct definitions.
		for( TFieldIterator<UFunction> Function = TFieldIterator<UFunction>(Class); Function && Function.GetStruct()==Class; ++Function )
		{
			if
			(	(Function->FunctionFlags & FUNC_Event)
			&&	(!Function->GetSuperFunction()) )
			{
				Ar.Logf( TEXT("struct %s_event%s_Parms\r\n"), Class->GetNameCPP(), Function->GetName() );
				Ar.Log( TEXT("{\r\n") );
				for( TFieldIterator<UProperty> It=TFieldIterator<UProperty>(*Function); It && (It->PropertyFlags&CPF_Parm); ++It )
				{
					Ar.Log( TEXT("    ") );
					It->ExportCpp( Ar, 1, 0 );
					Ar.Log( TEXT(";\r\n") );
				}
				Ar.Log( TEXT("};\r\n") );
			}
		}
		
		// Class definition.
		Ar.Logf( TEXT("class %s_API %s"), API, Class->GetNameCPP() );
		if( Class->GetSuperClass() )
			Ar.Logf( TEXT(" : public %s\r\n"), Class->GetSuperClass()->GetNameCPP() );
		Ar.Logf( TEXT("{\r\npublic:\r\n") );

		// All per-object properties defined in this class.
		UProperty* LastIt = NULL;
		for( TFieldIterator<UProperty> It = TFieldIterator<UProperty>(Class); It; ++It )
		{
			if( It.GetStruct()==Class && It->ElementSize )
			{
				Ar.Logf( appSpc(TextIndent+4) );
				It->ExportCpp( Ar, 0, 0 );

				bool doPack = false;

				if (It->IsA(UBoolProperty::StaticClass()))
				{
					if( !LastIt || !LastIt->IsA(UBoolProperty::StaticClass()) )
						doPack = true;
				}
				else
				{
					if ( LastIt == NULL )
						doPack = true;
					else if ( LastIt->IsA(UBoolProperty::StaticClass()) )
						doPack = true;
					else if ( LastIt->IsA(UByteProperty::StaticClass()) && !It->IsA(UByteProperty::StaticClass()) )
						doPack = true;
				}

				if ( doPack )
					Ar.Logf( TEXT(" GCC_PACK(%i)"), PROPERTY_ALIGNMENT );

				Ar.Logf( TEXT(";\r\n") );
			}
			LastIt = *It;
		}

		// C++ -> UnrealScript stubs.
		for( TFieldIterator<UFunction> Function = TFieldIterator<UFunction>(Class); Function && Function.GetStruct()==Class; ++Function )
			if( Function->FunctionFlags & FUNC_Native )
				Ar.Logf( TEXT("    DECLARE_FUNCTION(exec%s);\r\n"), Function->GetName() );

		// UnrealScript -> C++ proxies.
		for( TFieldIterator<UFunction> Function = TFieldIterator<UFunction>(Class); Function && Function.GetStruct()==Class; ++Function )
		{
			if
			(	(Function->FunctionFlags & FUNC_Event)
			&&	(!Function->GetSuperFunction()) )
			{
				// Return type.
				UProperty* Return = Function->GetReturnProperty();
				Ar.Log( TEXT("    ") );
				if( !Return )
					Ar.Log( TEXT("void") );
				else
					Return->ExportCppItem( Ar );

				// Function name and parms.
				INT ParmCount=0;
				Ar.Logf( TEXT(" event%s("), Function->GetName() );
				for( TFieldIterator<UProperty> It(*Function); It && (It->PropertyFlags&(CPF_Parm|CPF_ReturnParm))==CPF_Parm; ++It )
				{
					if( ParmCount++ )
						Ar.Log(TEXT(", "));
					It->ExportCpp( Ar, 0, 1 );
				}
				Ar.Log( TEXT(")\r\n") );

				// Function call.
				Ar.Log( TEXT("    {\r\n") );
				if( ParmCount || Return )
				{
					// Parms struct.
					Ar.Logf( TEXT("        %s_event%s_Parms Parms;\r\n"), Class->GetNameCPP(), Function->GetName() );

					// Parms struct initialization.
					for( TFieldIterator<UProperty> It=TFieldIterator<UProperty>(*Function); It && (It->PropertyFlags&(CPF_Parm|CPF_ReturnParm))==CPF_Parm; ++It )
					{
						if( It->ArrayDim>1 )
							Ar.Logf( TEXT("        appMemcpy(&Parms.%s,%s,sizeof(Parms.%s));\r\n"), It->GetName(), It->GetName(), It->GetName() );
						else
						{
							if ( It->IsA(UBoolProperty::StaticClass()) )   // need to handle bitfield byte order when passing into UnrealScript...  --ryan.
								Ar.Logf( TEXT("        Parms.%s=((%s) ? FIRST_BITFIELD : 0);\r\n"), It->GetName(), It->GetName() );
							else
								Ar.Logf( TEXT("        Parms.%s=%s;\r\n"), It->GetName(), It->GetName() );
						}
					}
					if( Return && !Cast<UStrProperty>(Return) )
						Ar.Logf( TEXT("        Parms.%s=0;\r\n"), Return->GetName() );
					Ar.Logf( TEXT("        ProcessEvent(FindFunctionChecked(%s_%s),&Parms);\r\n"), API, Function->GetName() );
				}
				else Ar.Logf( TEXT("        ProcessEvent(FindFunctionChecked(%s_%s),NULL);\r\n"), API, Function->GetName() );

				// Out parm copying.
				for( TFieldIterator<UProperty> It=TFieldIterator<UProperty>(*Function); It && (It->PropertyFlags&(CPF_Parm|CPF_ReturnParm))==CPF_Parm; ++It )
				{
					if( It->PropertyFlags & CPF_OutParm )
					{
						if( It->ArrayDim>1 )
							Ar.Logf( TEXT("        appMemcpy(&%s,&Parms.%s,sizeof(%s));\r\n"), It->GetName(), It->GetName(), It->GetName() );
						else
							Ar.Logf( TEXT("        %s=Parms.%s;\r\n"), It->GetName(), It->GetName() );
					}
				}

				// Return value.
				if( Return )
					Ar.Logf( TEXT("        return Parms.%s;\r\n"), Return->GetName() );
				Ar.Log( TEXT("    }\r\n") );
			}
		}

		// Code.
		Ar.Logf( TEXT("    DECLARE_CLASS(%s,"), Class->GetNameCPP() ); //warning: GetNameCPP uses static storage.
		Ar.Logf( TEXT("%s,0"), Class->GetSuperClass()->GetNameCPP() );
		if( Class->ClassFlags & CLASS_Transient      )
			Ar.Log( TEXT("|CLASS_Transient") );
		if( Class->ClassFlags & CLASS_Config )
			Ar.Log( TEXT("|CLASS_Config") );
		if( Class->ClassFlags & CLASS_NativeReplication )
			Ar.Log( TEXT("|CLASS_NativeReplication") );
		Ar.Logf( TEXT(")\r\n") );
		FString Filename =  FURL::DefaultSourceDir * Class->GetOuter()->GetName() * TEXT("Inc") * Class->GetNameCPP() + TEXT(".h");
		if( GFileManager->FileSize(*Filename) > 0 )
			Ar.Logf( TEXT("    #include \"%s.h\"\r\n"), Class->GetNameCPP() );
		else
			Ar.Logf( TEXT("    NO_DEFAULT_CONSTRUCTOR(%s)\r\n"), Class->GetNameCPP() );

		// End of class.
		Ar.Logf( TEXT("};\r\n") );

		// End.
		Ar.Logf( TEXT("\r\n") );
	}

	// Export all child classes that are tagged for export.
	RecursionDepth++;
	for( TObjectIterator<UClass> It; It; ++It )
		if( It->GetSuperClass()==Class )
			UExporter::ExportToOutputDevice( *It, this, Ar, TEXT("H"), TextIndent );
	RecursionDepth--;

	// Finish C++ header.
	if( RecursionDepth==0 )
	{
		Ar.Logf( TEXT("#endif\r\n") );
		Ar.Logf( TEXT("\r\n") );

		for( TObjectIterator<UClass> It; It; ++It )
			if( It->GetFlags() & RF_TagExp )
				for( TFieldIterator<UFunction> Function(*It); Function && Function.GetStruct()==*It; ++Function )
					if( Function->FunctionFlags & FUNC_Native )
						Ar.Logf( TEXT("AUTOGENERATE_FUNCTION(%s,%i,exec%s);\r\n"), It->GetNameCPP(), Function->iNative ? Function->iNative : -1, Function->GetName() );

		Ar.Logf( TEXT("\r\n") );
		Ar.Logf( TEXT("#ifndef NAMES_ONLY\r\n") );
		Ar.Logf( TEXT("#undef AUTOGENERATE_NAME\r\n") );
		Ar.Logf( TEXT("#undef AUTOGENERATE_FUNCTION\r\n") );
		Ar.Logf( TEXT("#endif\r\n") );

		Ar.Logf( TEXT("\r\n") );
		Ar.Logf( TEXT("#if SUPPORTS_PRAGMA_PACK\r\n") );
		Ar.Logf( TEXT("#pragma pack (pop)\r\n") );
		Ar.Logf( TEXT("#endif\r\n") );
	}

	return 1;
	unguard;
}
IMPLEMENT_CLASS(UClassExporterH);

/*------------------------------------------------------------------------------
	UClassExporterUC implementation.
------------------------------------------------------------------------------*/

void UClassExporterUC::StaticConstructor()
{
	guard(UClassExporterUC::StaticConstructor);

	SupportedClass = UClass::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("UC"));

	unguard;
}
UBOOL UClassExporterUC::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(UClassExporterUC::ExportText);
	UClass* Class = CastChecked<UClass>( Object );

	// Export script text.
	check(Class->Defaults.Num());
#if ADDITIONS_FIXES
	if( !Class->ScriptText )
		return 0;
#else
	check(Class->ScriptText);
#endif
	UExporter::ExportToOutputDevice( Class->ScriptText, NULL, Ar, TEXT("txt"), TextIndent );

	// Export default properties that differ from parent's.
	Ar.Log( TEXT("\r\n\r\ndefaultproperties\r\n{\r\n") );
	ExportProperties
	(
		Ar,
		Class,
		&Class->Defaults(0),
		TextIndent+4,
		Class->GetSuperClass(),
		Class->GetSuperClass() ? &Class->GetSuperClass()->Defaults(0) : NULL
	);
	Ar.Log( TEXT("}\r\n") );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(UClassExporterUC);

/*------------------------------------------------------------------------------
	USoundExporterWAV implementation.
------------------------------------------------------------------------------*/

void UPolysExporterT3D::StaticConstructor()
{
	guard(UPolysExporterT3D::StaticConstructor);

	SupportedClass = UPolys::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("T3D"));

	unguard;
}
UBOOL UPolysExporterT3D::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(UPolysExporterT3D::ExportText);
	UPolys* Polys = CastChecked<UPolys>( Object );

	Ar.Logf( TEXT("%sBegin PolyList\r\n"), appSpc(TextIndent) );
	for( INT i=0; i<Polys->Element.Num(); i++ )
	{
		FPoly* Poly = &Polys->Element(i);
		TCHAR TempStr[256];

		// Start of polygon plus group/item name if applicable.
		Ar.Logf( TEXT("%s   Begin Polygon"), appSpc(TextIndent) );
		if( Poly->ItemName != NAME_None )
			Ar.Logf( TEXT(" Item=%s"), *Poly->ItemName );
		if( Poly->Texture )
			Ar.Logf( TEXT(" Texture=%s"), Poly->Texture->GetPathName() );
		if( Poly->PolyFlags != 0 )
			Ar.Logf( TEXT(" Flags=%i"), Poly->PolyFlags );
		if( Poly->iLink != INDEX_NONE )
			Ar.Logf( TEXT(" Link=%i"), Poly->iLink );
		Ar.Logf( TEXT("\r\n") );

		// All coordinates.
		Ar.Logf( TEXT("%s      Origin   %s\r\n"), appSpc(TextIndent), SetFVECTOR(TempStr,&Poly->Base) );
		Ar.Logf( TEXT("%s      Normal   %s\r\n"), appSpc(TextIndent), SetFVECTOR(TempStr,&Poly->Normal) );
		if( Poly->PanU!=0 || Poly->PanV!=0 )
			Ar.Logf( TEXT("%s      Pan      U=%i V=%i\r\n"), appSpc(TextIndent), Poly->PanU, Poly->PanV );
		Ar.Logf( TEXT("%s      TextureU %s\r\n"), appSpc(TextIndent), SetFVECTOR(TempStr,&Poly->TextureU) );
		Ar.Logf( TEXT("%s      TextureV %s\r\n"), appSpc(TextIndent), SetFVECTOR(TempStr,&Poly->TextureV) );
		for( INT j=0; j<Poly->NumVertices; j++ )
			Ar.Logf( TEXT("%s      Vertex   %s\r\n"), appSpc(TextIndent), SetFVECTOR(TempStr,&Poly->Vertex[j]) );
		Ar.Logf( TEXT("%s   End Polygon\r\n"), appSpc(TextIndent) );
	}
	Ar.Logf( TEXT("%sEnd PolyList\r\n"), appSpc(TextIndent) );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(UPolysExporterT3D);

/*------------------------------------------------------------------------------
	UModelExporterT3D implementation.
------------------------------------------------------------------------------*/

void UModelExporterT3D::StaticConstructor()
{
	guard(UModelExporterT3D::StaticConstructor);

	SupportedClass = UModel::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("T3D"));

	unguard;
}
UBOOL UModelExporterT3D::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(UModelExporterT3D::ExportText);
	UModel* Model = CastChecked<UModel>( Object );

	Ar.Logf( TEXT("%sBegin Brush Name=%s\r\n"), appSpc(TextIndent), Model->GetName() );
	UExporter::ExportToOutputDevice( Model->Polys, NULL, Ar, Type, TextIndent+3 );
	Ar.Logf( TEXT("%sEnd Brush\r\n"), appSpc(TextIndent) );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(UModelExporterT3D);

/*------------------------------------------------------------------------------
	ULevelExporterT3D implementation.
------------------------------------------------------------------------------*/

void ULevelExporterT3D::StaticConstructor()
{
	guard(ULevelExporterT3D::StaticConstructor);

	SupportedClass = ULevel::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("T3D"));
	new(Formats)FString(TEXT("COPY"));

	unguard;
}
UBOOL ULevelExporterT3D::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(ULevelExporterT3D::ExportText);
	ULevel* Level = CastChecked<ULevel>( Object );

	for( FObjectIterator It; It; ++It )
		It->ClearFlags( RF_TagImp | RF_TagExp );

	Ar.Logf( TEXT("%sBegin Map\r\n"), appSpc(TextIndent) );
	UBOOL AllSelected = appStricmp(Type,TEXT("COPY"))!=0;
	for( INT iActor=0; iActor<Level->Actors.Num(); iActor++ )
	{
		AActor* Actor = Level->Actors(iActor);
		if( Actor && !Cast<ACamera>(Actor) && (AllSelected ||Actor->bSelected) )
		{
			Ar.Logf( TEXT("%sBegin Actor Class=%s Name=%s\r\n"), appSpc(TextIndent), Actor->GetClass()->GetName(), Actor->GetName() );
			ExportProperties( Ar, Actor->GetClass(), (BYTE*)Actor, TextIndent+3, Actor->GetClass(), &Actor->GetClass()->Defaults(0) );
			Ar.Logf( TEXT("%sEnd Actor\r\n"), appSpc(TextIndent) );
		}
	}

	// Export information about the first selected surface in the map.  Used for copying/pasting
	// information from poly to poly.
	Ar.Logf( TEXT("%sBegin Surface\r\n"), appSpc(TextIndent) );
	TCHAR TempStr[256];
	for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
	{
		FBspSurf *Poly = &Level->Model->Surfs(i);
		if( Poly->PolyFlags&PF_Selected )
		{
			Ar.Logf( TEXT("%sTEXTURE=%s\r\n"), appSpc(TextIndent+3), Poly->Texture->GetPathName() );
			Ar.Logf( TEXT("%sBASE      %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&(Level->Model->Points(Poly->pBase))) );
			Ar.Logf( TEXT("%sTEXTUREU  %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&(Level->Model->Vectors(Poly->vTextureU))) );
			Ar.Logf( TEXT("%sTEXTUREV  %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&(Level->Model->Vectors(Poly->vTextureV))) );
			Ar.Logf( TEXT("%sPAN       U=%i V=%i\r\n"), appSpc(TextIndent+3), Poly->PanU, Poly->PanV );
			Ar.Logf( TEXT("%sNORMAL    %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&(Level->Model->Vectors(Poly->vNormal))) );
			Ar.Logf( TEXT("%sPOLYFLAGS=%d\r\n"), appSpc(TextIndent+3), Poly->PolyFlags );
			break;
		}
	}
	Ar.Logf( TEXT("%sEnd Surface\r\n"), appSpc(TextIndent) );

	Ar.Logf( TEXT("%sEnd Map\r\n"), appSpc(TextIndent) );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(ULevelExporterT3D);

/*------------------------------------------------------------------------------
	ULevelExporterT3D_UE5 implementation.
------------------------------------------------------------------------------*/

void ULevelExporterT3D_UE5::StaticConstructor()
{
	guard(ULevelExporterT3D_UE5::StaticConstructor);

	SupportedClass = ULevel::StaticClass();
	bText = 1;
	new(Formats)FString(TEXT("T3D_UE5"));
	new(Formats)FString(TEXT("UE5"));

	unguard;
}

void ULevelExporterT3D_UE5::ExportActorUE5( FOutputDevice& Ar, AActor* Actor, INT Indent )
{
	guard(ULevelExporterT3D_UE5::ExportActorUE5);

	// Export actor in exact UE5 format
	// Build UE5-style actor attributes
	FString ParentActorString = TEXT("");  // UE1 doesn't have attachment system like UE5
	FString SocketNameString = TEXT("");
	FString GroupActor = TEXT("");
	FString GroupFolder = TEXT("");
	FString ActorFolderPath = TEXT("");
	FString CopyPasteId = TEXT("");
	FString ContentBundleGuid = TEXT("");
	FString ExternalDataLayer = TEXT("");

	// Generate archetype path (simplified for UE1)
	FString ArchetypePath = FString::Printf( TEXT("%s'%s'"),
		Actor->GetClass()->GetName(),
		Actor->GetClass()->GetName() );

	// Generate export path (simplified for UE1)
	FString ExportPath = FString::Printf( TEXT("%s'%s:%s'"),
		Actor->GetClass()->GetName(),
		Actor->GetOuter() ? Actor->GetOuter()->GetName() : TEXT("Level"),
		Actor->GetName() );

	// Export Begin Actor line with all UE5 attributes
	Ar.Logf( TEXT("%sBegin Actor Class=%s Name=%s Archetype=%s%s%s%s%s%s%s%s%s"),
		appSpc(Indent),
		Actor->GetClass()->GetPathName(),
		Actor->GetName(),
		*ArchetypePath,
		*ParentActorString,
		*SocketNameString,
		*GroupActor,
		*GroupFolder,
		*ActorFolderPath,
		*CopyPasteId,
		*ContentBundleGuid,
		*ExternalDataLayer );

	// Add ExportPath
	Ar.Logf( TEXT(" ExportPath=%s"), *ExportPath );
	Ar.Logf( TEXT("\r\n") );

	// Export all actor properties using the standard property export
	ExportProperties( Ar, Actor->GetClass(), (BYTE*)Actor, Indent+3, Actor->GetClass(), &Actor->GetClass()->Defaults(0) );

	Ar.Logf( TEXT("%sEnd Actor\r\n"), appSpc(Indent) );

	unguard;
}

UBOOL ULevelExporterT3D_UE5::ExportText( UObject* Object, const TCHAR* Type, FOutputDevice& Ar, FFeedbackContext* Warn )
{
	guard(ULevelExporterT3D_UE5::ExportText);
	ULevel* Level = CastChecked<ULevel>( Object );

	// Clear object flags
	for( FObjectIterator It; It; ++It )
		It->ClearFlags( RF_TagImp | RF_TagExp );

	// Export UE5-style header - Begin Map with package name
	UPackage* MapPackage = Cast<UPackage>(Level->GetOuter());
	if( MapPackage )
	{
		Ar.Logf( TEXT("%sBegin Map Name=%s\r\n"), appSpc(TextIndent), MapPackage->GetName() );
	}
	else
	{
		Ar.Logf( TEXT("%sBegin Map\r\n"), appSpc(TextIndent) );
	}

	// Determine if we're exporting all actors or just selected
	UBOOL bAllActors = appStricmp(Type,TEXT("COPY"))!=0;

	TextIndent += 3;

	// Begin Level section with proper naming
	if( appStricmp(Type, TEXT("COPY")) == 0 )
	{
		// For copy/paste, don't name the level
		Ar.Logf( TEXT("%sBegin Level\r\n"), appSpc(TextIndent) );
	}
	else
	{
		// For export, name the level
		Ar.Logf( TEXT("%sBegin Level NAME=%s\r\n"), appSpc(TextIndent), Level->GetName() );
	}

	TextIndent += 3;

	// Export all actors in UE5 format
	for( INT iActor=0; iActor<Level->Actors.Num(); iActor++ )
	{
		AActor* Actor = Level->Actors(iActor);
		// Skip cameras and check selection
		if( Actor && !Cast<ACamera>(Actor) && (bAllActors || Actor->bSelected) )
		{
			ExportActorUE5( Ar, Actor, TextIndent );
		}
	}

	TextIndent -= 3;
	Ar.Logf( TEXT("%sEnd Level\r\n"), appSpc(TextIndent) );

	// Export surface information in UE5 format
	Ar.Logf( TEXT("%sBegin Surface\r\n"), appSpc(TextIndent) );
	TCHAR TempStr[256];
	for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
	{
		FBspSurf *Poly = &Level->Model->Surfs(i);
		if( Poly->PolyFlags&PF_Selected )
		{
			FVector pBase = Level->Model->Points(Poly->pBase);
			FVector vTextureU = Level->Model->Vectors(Poly->vTextureU);
			FVector vTextureV = Level->Model->Vectors(Poly->vTextureV);
			FVector vNormal = Level->Model->Vectors(Poly->vNormal);

			Ar.Logf( TEXT("%sTEXTURE=%s\r\n"), appSpc(TextIndent+3), Poly->Texture->GetPathName() );
			Ar.Logf( TEXT("%sBASE      %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&pBase) );
			Ar.Logf( TEXT("%sTEXTUREU  %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&vTextureU) );
			Ar.Logf( TEXT("%sTEXTUREV  %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&vTextureV) );
			Ar.Logf( TEXT("%sNORMAL    %s\r\n"), appSpc(TextIndent+3), SetFVECTOR(TempStr,&vNormal) );
			Ar.Logf( TEXT("%sPOLYFLAGS=%d\r\n"), appSpc(TextIndent+3), Poly->PolyFlags );
			break;
		}
	}
	Ar.Logf( TEXT("%sEnd Surface\r\n"), appSpc(TextIndent) );

	TextIndent -= 3;
	Ar.Logf( TEXT("%sEnd Map\r\n"), appSpc(TextIndent) );

	return 1;
	unguard;
}
IMPLEMENT_CLASS(ULevelExporterT3D_UE5);
