#pragma once
//#include <imgui.h>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <unordered_map>
#include <fstream>

#include "Engine.h"
#include "UnRender.h"

#pragma pack(push,8)
#include "imgui_impl_unreal.h"

#include "json.hpp"
using json = nlohmann::json;
#pragma pack(pop)

#include "RenderImGui.h"

struct URenderImGuiNewGameWindow_eventStartGame_Parms
{
    UBOOL RenewalLevels;
    UBOOL RenewalGameplay;
	FString SelectedMutators;
};

struct GameModifier {
    std::string id;
    std::string name;
    std::string description;
    std::string category;
    bool selected = false;
    float animation_progress = 0.0f;
    std::chrono::steady_clock::time_point last_update;
    ImVec4 category_color;
};

struct GameConfig {
    std::unordered_map<std::string, ImVec4> category_colors;
    std::vector<GameModifier> modifiers;
};

static void to_json(json& j, const GameModifier& gm) {
    j = json{
        {"id", gm.id},
        {"name", gm.name},
        {"description", gm.description},
        {"category", gm.category},
        {"selected", gm.selected}
        // Exclude category_color, animation_progress and last_update from save
    };
}

static void from_json(const json& j, GameModifier& gm) {
    j.at("id").get_to(gm.id);
    j.at("name").get_to(gm.name);
    j.at("description").get_to(gm.description);
    j.at("category").get_to(gm.category);
    j.at("selected").get_to(gm.selected);

    gm.animation_progress = 0.0f;
    gm.last_update = std::chrono::steady_clock::now();
}

static void to_json(json& j, const GameConfig& config) {
    json color_json;
    for (auto it = config.category_colors.begin(); it != config.category_colors.end(); ++it) {
        const std::string& cat = it->first;
        const ImVec4& color = it->second;
        color_json[cat] = { color.x, color.y, color.z, color.w };
    }
    j = json{
        {"category_colors", color_json},
        {"modifiers", config.modifiers}
    };
}

static void from_json(const json& j, GameConfig& config) {
    config.category_colors.clear();
    if (j.find("category_colors") != j.end()) {
        const json& colors = j.at("category_colors");
        for (auto it = colors.begin(); it != colors.end(); ++it) {
            const std::string& cat = it.key();
            const std::vector<float>& c = it.value().get<std::vector<float>>();
            if (c.size() == 4)
                config.category_colors[cat] = ImVec4(c[0], c[1], c[2], c[3]);
        }
    }
    config.modifiers = j.at("modifiers").get<std::vector<GameModifier>>();
}

static GameConfig load_config(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) return {};

    json j;
    file >> j;

    return j.get<GameConfig>();
}

struct CardAnimationState {
    float hover_progress = 0.0f;
    float selection_progress = 0.0f;
    bool is_hovered = false;
    std::chrono::steady_clock::time_point last_frame;
};

class RENDERIMGUI_API URenderImGuiNewGameWindow : public URenderImGuiWindow
{
    DECLARE_CLASS(URenderImGuiNewGameWindow,URenderImGuiWindow,CLASS_Transient,RenderImGui)

    // Constructors:
    URenderImGuiNewGameWindow();

    void Paint(FSceneNode* Frame);
    void Shown();
    void Hidden();
    void Closed();
    void Resized();

private:
    bool show_popup = false;
    bool is_hard_mode = false;
    bool is_modded_maps = false;
    std::unordered_map<std::string, CardAnimationState> card_animations;
    GameConfig Config;
    
    // Responsive scaling
    float ui_scale = 1.0f;
    ImVec2 viewport_size;
    
    // Visual constants (will be scaled)
    float GetScaledValue(float base_value) const { return base_value * ui_scale; }
    float CARD_PADDING;
    float CARD_SPACING;
    float CARD_ROUNDING;
    static constexpr float ANIMATION_SPEED = 4.0f;
    static constexpr float HOVER_ANIMATION_SPEED = 6.0f;
    
    // Color scheme
    ImVec4 GetCategoryColor(const std::string& category) const;
    ImVec4 LerpColor(const ImVec4& a, const ImVec4& b, float t) const;
    
    void UpdateUIScale();
    void InitializeModifiers();
    void UpdateAnimations();
    void RenderDifficultySection();
    void RenderMapTypeSection();
    void RenderModifiersSection();
    void RenderModifierCard(GameModifier& modifier, const ImVec2& card_size);
    void RenderToggleCard(const char* title, const char* enabled_desc, const char* disabled_desc, 
                         bool& value, const ImVec4& enabled_color, const ImVec4& disabled_color);
    int GetSelectedModifierCount() const;
    ImVec2 CalculateOptimalCardSize(int columns) const;
    
public:
    void ShowPopup() { show_popup = true; }
    void HidePopup() { show_popup = false; }
    bool IsHardMode() const { return is_hard_mode; }
    bool IsModdedMaps() const { return is_modded_maps; }
    std::vector<std::string> GetSelectedModifiers() const;
};
IMPLEMENT_CLASS(URenderImGuiNewGameWindow);

URenderImGuiNewGameWindow::URenderImGuiNewGameWindow()
{
    // add more padding in unrealscript if this fails
    check(GetClass()->GetPropertiesSize() >= sizeof(ThisClass));
    
    is_hard_mode = true;
    is_modded_maps = true;

    Config = load_config("GameConfig.json");

    InitializeModifiers();
}

void URenderImGuiNewGameWindow::UpdateUIScale() {
    viewport_size = ImGui::GetMainViewport()->Size;

    // Base resolution: 800x600
    // Target resolution: 1920x1080
    float base_height = 600.0f;
    float scale_y = viewport_size.y / base_height;

    // Clamp scale between reasonable bounds and make text bigger at higher resolutions
    ui_scale = Clamp(scale_y, 0.8f, 2.5f);

    // Apply font scaling for better text readability
    ImGui::GetIO().FontGlobalScale = ui_scale;

    // Update scaled constants
    CARD_PADDING = GetScaledValue(12.0f);
    CARD_SPACING = GetScaledValue(8.0f);
    CARD_ROUNDING = GetScaledValue(8.0f);

    // Scale style variables
    ImGuiStyle& style = ImGui::GetStyle();
    //style = ImGuiStyle();
    //style.ScaleAllSizes(ui_scale);

    //style.WindowPadding         = ImVec2(8, 8) * ui_scale;
    //style.FramePadding          = ImVec2(6, 4) * ui_scale;
    //style.ItemSpacing           = ImVec2(8, 6) * ui_scale;
    style.ScrollbarSize         = 8.0f * ui_scale;
    style.GrabMinSize           = 10.0f * ui_scale;
    style.WindowRounding        = 8.0f * ui_scale;
    style.FrameRounding         = 4.0f * ui_scale;
    style.ScrollbarRounding     = 4.0f * ui_scale;
}

void URenderImGuiNewGameWindow::InitializeModifiers() {
#if 0
    installed_modifiers = {
        {"speed-boost", "Speed Boost", "Increases player movement speed by 50%. Great for speedruns and fast-paced gameplay.", "Movement", false, 0.0f, std::chrono::steady_clock::now()},
        {"infinite-ammo", "Infinite Ammo", "Removes ammunition limits for all weapons. Focus on strategy without resource management.", "Combat", false, 0.0f, std::chrono::steady_clock::now()},
        {"night-vision", "Night Vision", "Enhanced visibility in dark areas. Reveals hidden secrets and improves exploration.", "Visual", false, 0.0f, std::chrono::steady_clock::now()},
        {"double-jump", "Double Jump", "Allows players to jump twice in mid-air. Opens up new traversal possibilities.", "Movement", false, 0.0f, std::chrono::steady_clock::now()},
        {"health-regen", "Health Regeneration", "Slowly regenerates health over time when not taking damage. Reduces reliance on health items.", "Survival", false, 0.0f, std::chrono::steady_clock::now()},
        {"time-slow", "Bullet Time", "Activate slow-motion mode during combat. Adds a tactical element to firefights.", "Combat", false, 0.0f, std::chrono::steady_clock::now()},
        {"stealth-mode", "Stealth Enhancement", "Improved sneaking mechanics with reduced detection radius and silent movement.", "Stealth", false, 0.0f, std::chrono::steady_clock::now()},
        {"weapon-upgrade", "Enhanced Weapons", "All weapons deal increased damage and have improved accuracy and range.", "Combat", false, 0.0f, std::chrono::steady_clock::now()}
    };
#endif

#if 0
    installed_modifiers = {
        {"lantern", "Lantern", "Gives the lantern item that emits light.", "Cut Content", false, 0.0f, std::chrono::steady_clock::now()},
        {"defensive-spells", "Defensive Spells", "Brings back defensive spells that were cut from the game.", "Cut Content", false, 0.0f, std::chrono::steady_clock::now()},
        {"minigun", "Minigun", "Replaces revolver with the Minigun from Serious Sam.", "Fun", false, 0.0f, std::chrono::steady_clock::now()},
    };
#endif
    
    // Set category colors
    for (auto& modifier : Config.modifiers) {
        modifier.category_color = GetCategoryColor(modifier.category);
    }
}

ImVec4 URenderImGuiNewGameWindow::GetCategoryColor(const std::string& category) const {
    auto it = Config.category_colors.find(category);
    if (it != Config.category_colors.end()) {
        return it->second;
    }
    return ImVec4(0.6f, 0.6f, 0.6f, 1.0f); // Default gray
}

ImVec4 URenderImGuiNewGameWindow::LerpColor(const ImVec4& a, const ImVec4& b, float t) const {
    return ImVec4(
        a.x + (b.x - a.x) * t,
        a.y + (b.y - a.y) * t,
        a.z + (b.z - a.z) * t,
        a.w + (b.w - a.w) * t
    );
}

void URenderImGuiNewGameWindow::UpdateAnimations() {
    auto now = std::chrono::steady_clock::now();
    
    // Update modifier animations
    for (auto& modifier : Config.modifiers) {
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - modifier.last_update).count();
        float delta_time = duration / 1000.0f;
        modifier.last_update = now;
        
        float target = modifier.selected ? 1.0f : 0.0f;
        float diff = target - modifier.animation_progress;
        modifier.animation_progress += diff * ANIMATION_SPEED * delta_time;
        modifier.animation_progress = Clamp(modifier.animation_progress, 0.0f, 1.0f);
    }
    
    // Update card hover animations
    for (auto& pair : card_animations) {
        const auto& id = pair.first;
        auto& anim = pair.second;

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - anim.last_frame).count();
        float delta_time = duration / 1000.0f;
        anim.last_frame = now;
        
        float hover_target = anim.is_hovered ? 1.0f : 0.0f;
        float hover_diff = hover_target - anim.hover_progress;
        anim.hover_progress += hover_diff * HOVER_ANIMATION_SPEED * delta_time;
        anim.hover_progress = Clamp(anim.hover_progress, 0.0f, 1.0f);
        
        // Reset hover state for next frame
        anim.is_hovered = false;
    }
}

void URenderImGuiNewGameWindow::RenderToggleCard(const char* title, const char* enabled_desc, const char* disabled_desc, 
                                        bool& value, const ImVec4& enabled_color, const ImVec4& disabled_color) {
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    ImVec2 canvas_pos = ImGui::GetCursorScreenPos();
    ImVec2 canvas_size = ImVec2(ImGui::GetContentRegionAvail().x, GetScaledValue(95.0f));
    
    // Card background
    ImVec4 bg_color = LerpColor(
        ImVec4(0.15f, 0.15f, 0.15f, 1.0f), 
        ImVec4(0.2f, 0.2f, 0.2f, 1.0f), 
        value ? 1.0f : 0.0f
    );
    
    // Border color - grey by default, green when enabled
    ImVec4 border_color = value ? ImVec4(0.2f, 0.8f, 0.3f, 1.0f) : ImVec4(0.4f, 0.4f, 0.4f, 1.0f);
    
    // Draw card background
    draw_list->AddRectFilled(
        canvas_pos,
        ImVec2(canvas_pos.x + canvas_size.x, canvas_pos.y + canvas_size.y),
        ImGui::ColorConvertFloat4ToU32(bg_color),
        CARD_ROUNDING
    );
    
    // Draw border
    draw_list->AddRect(
        canvas_pos,
        ImVec2(canvas_pos.x + canvas_size.x, canvas_pos.y + canvas_size.y),
        ImGui::ColorConvertFloat4ToU32(border_color),
        CARD_ROUNDING,
        0,
        GetScaledValue(2.0f)
    );
    
    // Make the entire card clickable FIRST (before other UI elements)
    ImGui::SetCursorScreenPos(canvas_pos);
    ImGui::InvisibleButton(("toggle_card_" + std::string(title)).c_str(), canvas_size);
    if (ImGui::IsItemClicked()) {
        value = !value;
    }
    
    // Content
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING));
    
    // Title
    ImGui::Text("%s", title);
    
    // Checkbox positioned relative to canvas, not cursor
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + canvas_size.x - GetScaledValue(30.0f), canvas_pos.y + CARD_PADDING));
    
    ImGui::PushStyleColor(ImGuiCol_FrameBg, value ? ImGui::ColorConvertFloat4ToU32(ImVec4(0.2f, 0.8f, 0.3f, 1.0f)) : ImGui::ColorConvertFloat4ToU32(ImVec4(0.3f, 0.3f, 0.3f, 1.0f)));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, value ? ImGui::ColorConvertFloat4ToU32(ImVec4(0.3f, 0.9f, 0.4f, 1.0f)) : ImGui::ColorConvertFloat4ToU32(ImVec4(0.4f, 0.4f, 0.4f, 1.0f)));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
    
    std::string checkbox_id = std::string("##") + title;
    ImGui::Checkbox(checkbox_id.c_str(), &value);
    
    ImGui::PopStyleColor(3);
    
    // Description
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + GetScaledValue(35.0f)));
    ImGui::PushStyleColor(ImGuiCol_Text, value ? enabled_color : disabled_color);
    ImGui::Text("%s", value ? "Enabled" : "Disabled");
    ImGui::PopStyleColor();
    
    // Detailed description with proper text wrapping
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + GetScaledValue(55.0f)));
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.7f, 0.7f, 0.7f, 1.0f));
    
    // Calculate available width for text (excluding padding and some margin for checkbox)
    float available_text_width = canvas_size.x - CARD_PADDING * 2 - GetScaledValue(10.0f);
    float available_text_height = canvas_size.y - GetScaledValue(10.0f) - CARD_PADDING;
    
    // Create a child region to properly contain and clip the text
    ImGui::BeginChild(("toggle_desc_" + std::string(title)).c_str(), 
                     ImVec2(available_text_width, available_text_height), 
                     ImGuiChildFlags_None, 
                     ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoInputs);
    
    ImGui::PushTextWrapPos(ImGui::GetCursorPosX() + available_text_width);
    ImGui::TextWrapped("%s", value ? enabled_desc : disabled_desc);
    ImGui::PopTextWrapPos();
    
    ImGui::EndChild();
    ImGui::PopStyleColor();
    
    // Advance cursor
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x, canvas_pos.y + canvas_size.y + CARD_SPACING));
}

void URenderImGuiNewGameWindow::RenderDifficultySection() {
    RenderToggleCard(
        "Gameplay Changes",
        "Rebalanced gameplay, restored cut features.\nThis option can be changed at any time from the main menu.",
        "Original gameplay.\nThis option can be changed at any time from the main menu.",
        is_hard_mode,
        ImVec4(0.2f, 0.8f, 0.2f, 1.0f),  // Green
        ImVec4(0.8f, 0.2f, 0.2f, 1.0f) // Red
    );
}

void URenderImGuiNewGameWindow::RenderMapTypeSection() {
    RenderToggleCard(
        "Level Enhancements",
        "Original levels with bug fixes, reduced number of loading zones, and other improvements.",
        "Original levels.",
        is_modded_maps,
        ImVec4(0.2f, 0.8f, 0.2f, 1.0f),  // Green
        ImVec4(0.8f, 0.2f, 0.2f, 1.0f) // Red
    );
}

ImVec2 URenderImGuiNewGameWindow::CalculateOptimalCardSize(int columns) const {

    ImGuiStyle& style = ImGui::GetStyle();

    // Get the content region available to the window
    float available_width = ImGui::GetContentRegionAvail().x;

    // Subtract frame padding
    available_width -= style.FramePadding.x * 2;

    // Always fit exactly X cards side by side with proper spacing
    float total_spacing = (columns - 1) * CARD_SPACING;
    float card_width = (available_width - total_spacing) / columns;

    return ImVec2(card_width, GetScaledValue(110.0f));
}

void URenderImGuiNewGameWindow::RenderModifierCard(GameModifier& modifier, const ImVec2& card_size) {
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    ImVec2 canvas_pos = ImGui::GetCursorScreenPos();
    
    // Get or create animation state
    auto& anim = card_animations[modifier.id];
    if (anim.last_frame.time_since_epoch().count() == 0) {
        anim.last_frame = std::chrono::steady_clock::now();
    }
    
    // Check if mouse is hovering
    ImVec2 mouse_pos = ImGui::GetMousePos();
    bool is_hovering = mouse_pos.x >= canvas_pos.x && mouse_pos.x <= canvas_pos.x + card_size.x &&
                      mouse_pos.y >= canvas_pos.y && mouse_pos.y <= canvas_pos.y + card_size.y;
    anim.is_hovered = is_hovering;
    
    // Color calculations
    ImVec4 base_bg = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);
    ImVec4 selected_bg = ImVec4(0.18f, 0.18f, 0.18f, 1.0f);
    ImVec4 hover_bg = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);
    
    // Interpolate background color
    ImVec4 bg_color = LerpColor(base_bg, selected_bg, modifier.animation_progress);
    bg_color = LerpColor(bg_color, hover_bg, anim.hover_progress * 0.5f);
    
    // Border color with category color influence
    ImVec4 border_color = LerpColor(
        ImVec4(0.3f, 0.3f, 0.3f, 1.0f),
        modifier.category_color,
        modifier.animation_progress
    );
    border_color = LerpColor(border_color, ImVec4(1.0f, 1.0f, 1.0f, 1.0f), anim.hover_progress * 0.3f);
    
    // Draw card background with proper rounded corners
    draw_list->AddRectFilled(
        canvas_pos,
        ImVec2(canvas_pos.x + card_size.x, canvas_pos.y + card_size.y),
        ImGui::ColorConvertFloat4ToU32(bg_color),
        CARD_ROUNDING
    );
    
    // Draw border with precise positioning
    float border_thickness = GetScaledValue(1.0f + modifier.animation_progress * 1.5f + anim.hover_progress * 0.5f);
    
    draw_list->AddRect(
        canvas_pos,
        ImVec2(canvas_pos.x + card_size.x, canvas_pos.y + card_size.y),
        ImGui::ColorConvertFloat4ToU32(border_color),
        CARD_ROUNDING,
        0,
        border_thickness
    );
    
#if 0
     Selection indicator (left border accent) - fix left side positioning
    if (modifier.animation_progress > 0.01f) {
        ImVec4 accent_color = modifier.category_color;
        accent_color.w = modifier.animation_progress;
        
        // Draw accent strip with proper positioning
        draw_list->AddRectFilled(
            ImVec2(canvas_pos.x, canvas_pos.y + CARD_ROUNDING * 0.5f),
            ImVec2(canvas_pos.x + GetScaledValue(4.0f), canvas_pos.y + card_size.y - CARD_ROUNDING * 0.5f),
            ImGui::ColorConvertFloat4ToU32(accent_color)
        );
        
        // Add rounded corners for the accent strip
        draw_list->AddRectFilled(
            ImVec2(canvas_pos.x, canvas_pos.y + CARD_ROUNDING),
            ImVec2(canvas_pos.x + GetScaledValue(4.0f), canvas_pos.y + CARD_ROUNDING * 2),
            ImGui::ColorConvertFloat4ToU32(accent_color),
            CARD_ROUNDING,
            ImDrawFlags_RoundCornersTopLeft
        );
        
        draw_list->AddRectFilled(
            ImVec2(canvas_pos.x, canvas_pos.y + card_size.y - CARD_ROUNDING * 2),
            ImVec2(canvas_pos.x + GetScaledValue(4.0f), canvas_pos.y + card_size.y - CARD_ROUNDING),
            ImGui::ColorConvertFloat4ToU32(accent_color),
            CARD_ROUNDING,
            ImDrawFlags_RoundCornersBottomLeft
        );
    }
#endif
    
    // Content area
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING));
    
    // Checkbox (invisible, for interaction)
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0, 0, 0, 0));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0, 0, 0, 0));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0, 0, 0, 0));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0, 0, 0, 0));
    
    std::string checkbox_id = "##" + modifier.id;
    bool old_selected = modifier.selected;
    ImGui::Checkbox(checkbox_id.c_str(), &modifier.selected);
    
    ImGui::PopStyleColor(4);
    
    // Custom checkbox visual
    float checkbox_size = GetScaledValue(16.0f);
    ImVec2 checkbox_pos = ImVec2(canvas_pos.x + card_size.x - CARD_PADDING - checkbox_size, canvas_pos.y + CARD_PADDING);
    ImVec4 check_bg = modifier.selected ? modifier.category_color : ImVec4(0.3f, 0.3f, 0.3f, 1.0f);
    
    draw_list->AddRectFilled(
        checkbox_pos,
        ImVec2(checkbox_pos.x + checkbox_size, checkbox_pos.y + checkbox_size),
        ImGui::ColorConvertFloat4ToU32(check_bg),
        GetScaledValue(3.0f)
    );
    
    if (modifier.selected) {
        // Checkmark
        float line_thickness = GetScaledValue(2.0f);
        draw_list->AddLine(
            ImVec2(checkbox_pos.x + checkbox_size * 0.25f, checkbox_pos.y + checkbox_size * 0.5f),
            ImVec2(checkbox_pos.x + checkbox_size * 0.45f, checkbox_pos.y + checkbox_size * 0.7f),
            ImGui::ColorConvertFloat4ToU32(ImVec4(1, 1, 1, 1)),
            line_thickness
        );
        draw_list->AddLine(
            ImVec2(checkbox_pos.x + checkbox_size * 0.45f, checkbox_pos.y + checkbox_size * 0.7f),
            ImVec2(checkbox_pos.x + checkbox_size * 0.75f, checkbox_pos.y + checkbox_size * 0.3f),
            ImGui::ColorConvertFloat4ToU32(ImVec4(1, 1, 1, 1)),
            line_thickness
        );
    }
    
    // Title
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING));
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
    
    // Calculate available width for title (excluding checkbox area)
    float title_width = card_size.x - CARD_PADDING * 2 - checkbox_size - GetScaledValue(5.0f);
    ImGui::PushTextWrapPos(canvas_pos.x + CARD_PADDING + title_width);
    ImGui::TextWrapped("%s", modifier.name.c_str());
    ImGui::PopTextWrapPos();
    ImGui::PopStyleColor();
    
    // Category badge
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING + GetScaledValue(20.0f)));
    ImVec4 badge_color = modifier.category_color;
    badge_color.w = 0.3f;
    
    ImVec2 badge_text_size = ImGui::CalcTextSize(modifier.category.c_str());
    ImVec2 badge_size = ImVec2(badge_text_size.x + GetScaledValue(8.0f), badge_text_size.y + GetScaledValue(8.0f));
    
    // Ensure badge doesn't overflow
    float max_badge_width = card_size.x - CARD_PADDING * 2;
    if (badge_size.x > max_badge_width) {
        badge_size.x = max_badge_width;
    }
    
    draw_list->AddRectFilled(
        ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING + GetScaledValue(20.0f)),
        ImVec2(canvas_pos.x + CARD_PADDING + badge_size.x, canvas_pos.y + CARD_PADDING + GetScaledValue(20.0f) + badge_size.y),
        ImGui::ColorConvertFloat4ToU32(badge_color),
        GetScaledValue(4.0f)
    );
    
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING + GetScaledValue(4.0f), canvas_pos.y + CARD_PADDING + GetScaledValue(22.0f)));
    ImGui::PushStyleColor(ImGuiCol_Text, modifier.category_color);
    ImGui::Text("%s", modifier.category.c_str());
    ImGui::PopStyleColor();
    
    // Description
    ImGui::SetCursorScreenPos(ImVec2(canvas_pos.x + CARD_PADDING, canvas_pos.y + CARD_PADDING + GetScaledValue(45.0f)));
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.8f, 0.8f, 0.8f, 1.0f));
    
    // Calculate available space for description
    float desc_width = card_size.x - CARD_PADDING * 2;
    float desc_height = card_size.y - CARD_PADDING * 2 - GetScaledValue(45.0f);
    
    // Wrap text to fit card width
    ImGui::PushTextWrapPos(canvas_pos.x + CARD_PADDING + desc_width);
    
    // Create a child region to clip text that's too long - make it non-interactive
    ImGui::BeginChild(("desc_" + modifier.id).c_str(), ImVec2(desc_width, desc_height), ImGuiChildFlags_None, ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoInputs);
    ImGui::TextWrapped("%s", modifier.description.c_str());
    ImGui::EndChild();
    
    ImGui::PopTextWrapPos();
    ImGui::PopStyleColor();
    
    // Make the entire card clickable LAST - and reset cursor position properly
    ImGui::SetCursorScreenPos(canvas_pos);
    ImGui::InvisibleButton(("card_" + modifier.id).c_str(), card_size);
    if (ImGui::IsItemClicked()) {
        modifier.selected = !modifier.selected;
    }
    
    // Advance cursor properly for grid layout - only advance Y for the last card in a row
    // Don't advance cursor here, let the parent handle layout
}

void URenderImGuiNewGameWindow::RenderModifiersSection() {
    int selected_count = GetSelectedModifierCount();
    
    // Section header
    ImGui::Text("Game Modifiers");
    ImGui::SameLine();
    
    // Right-align the counter with enhanced styling
    std::string counter_text = std::to_string(selected_count) + " selected";
    float text_width = ImGui::CalcTextSize(counter_text.c_str()).x;
    float offset = ImGui::GetContentRegionAvail().x - text_width;
    if (offset > 0.0f) {
        ImGui::SetCursorPosX(ImGui::GetCursorPosX() + offset);
    }
    
    ImVec4 counter_color = selected_count > 0 ? ImVec4(0.2f, 0.8f, 0.4f, 1.0f) : ImVec4(0.6f, 0.6f, 0.6f, 1.0f);
    ImGui::PushStyleColor(ImGuiCol_Text, counter_color);
    ImGui::Text("%s", counter_text.c_str());
    ImGui::PopStyleColor();
    
    ImGui::Spacing();
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.7f, 0.7f, 0.7f, 1.0f));
    ImGui::TextWrapped("Modify your game with installed modifiers. Click cards to select any combination.");
    ImGui::PopStyleColor();
    ImGui::Spacing();

    ImGui::Separator();
    ImGui::Spacing();
    
    if (Config.modifiers.empty()) {
        // Enhanced empty state
        ImGui::Spacing();
        ImGui::Spacing();
        
        ImVec2 empty_size = ImVec2(ImGui::GetContentRegionAvail().x, GetScaledValue(100.0f));
        ImVec2 empty_pos = ImGui::GetCursorScreenPos();
        
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        draw_list->AddRectFilled(
            empty_pos,
            ImVec2(empty_pos.x + empty_size.x, empty_pos.y + empty_size.y),
            ImGui::ColorConvertFloat4ToU32(ImVec4(0.1f, 0.1f, 0.1f, 1.0f)),
            CARD_ROUNDING
        );
        
        draw_list->AddRect(
            empty_pos,
            ImVec2(empty_pos.x + empty_size.x, empty_pos.y + empty_size.y),
            ImGui::ColorConvertFloat4ToU32(ImVec4(0.3f, 0.3f, 0.3f, 1.0f)),
            CARD_ROUNDING,
            0,
            GetScaledValue(1.0f)
        );
        
        ImGui::SetCursorScreenPos(ImVec2(empty_pos.x + empty_size.x * 0.5f - GetScaledValue(80.0f), empty_pos.y + GetScaledValue(30.0f)));
        ImGui::Text("No modifiers installed");
        
        ImGui::SetCursorScreenPos(ImVec2(empty_pos.x + empty_size.x * 0.5f - GetScaledValue(120.0f), empty_pos.y + GetScaledValue(50.0f)));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
        ImGui::Text("Visit the mod page to install game modifiers");
        ImGui::PopStyleColor();
        
        ImGui::SetCursorScreenPos(ImVec2(empty_pos.x, empty_pos.y + empty_size.y + CARD_SPACING));
    } else {
        // Calculate card size for exactly 3 columns
        int columns = 3;
        ImVec2 card_size = CalculateOptimalCardSize(columns);
        
        // Calculate total content height needed
        int rows = (Config.modifiers.size() + columns - 1) / columns; // Ceiling division
        float total_content_height = rows * (card_size.y + CARD_SPACING) - CARD_SPACING; // Remove last spacing
        
        // Scrollable region for modifiers
        float scroll_height = GetScaledValue(200.0f);

		// Account for scrollbar if needed
        if (total_content_height > scroll_height) {
            card_size.x -= ImGui::GetStyle().ScrollbarSize / columns;
        }

        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.5f)); // Transparent background
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, GetScaledValue(12.0f));
        if (ImGui::BeginChild("ModifiersScrollRegion", ImVec2(0, scroll_height), ImGuiChildFlags_FrameStyle , ImGuiWindowFlags_None)) {
            for (size_t i = 0; i < Config.modifiers.size(); ++i) {
                // Start new row
                if (i % columns == 0 && i > 0) {
                    // Move to next row
                    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + CARD_SPACING);
                }
                
                // Add horizontal spacing between cards in the same row
                if (i % columns != 0) {
                    ImGui::SameLine(0, CARD_SPACING);
                }
                
                RenderModifierCard(Config.modifiers[i], card_size);
            }
            
            // Add some padding at the bottom
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + CARD_SPACING);
        }
        ImGui::EndChild();

        ImGui::PopStyleVar();
        ImGui::PopStyleColor();
    }
}

int URenderImGuiNewGameWindow::GetSelectedModifierCount() const {
    int count = 0;
    for (const auto& modifier : Config.modifiers) {
        if (modifier.selected) {
            count++;
        }
    }
    return count;
}

std::vector<std::string> URenderImGuiNewGameWindow::GetSelectedModifiers() const {
    std::vector<std::string> selected;
    for (const auto& modifier : Config.modifiers) {
        if (modifier.selected) {
            selected.push_back(modifier.id);
        }
    }
    return selected;
}

void URenderImGuiNewGameWindow::Shown()
{
#if 0
    if (!GRendererSupportsAlpaBlend)
    {
        Close();

        URenderImGuiNewGameWindow_eventStartGame_Parms Parms;
        Parms.RenewalLevels = 1;
        Parms.RenewalGameplay = 1;
        Parms.SelectedMutators = TEXT("");

        CallEvent(TEXT("StartGame"), &Parms);

        return;
    }
#endif

    ShowPopup();
}

void URenderImGuiNewGameWindow::Hidden()
{
    HidePopup();
}

void URenderImGuiNewGameWindow::Closed()
{
    HidePopup();
}

void URenderImGuiNewGameWindow::Resized()
{
    //asdad
}

#if 0
#include "imgui_internal.h"
void ToggleButton(const char* str_id, bool* v)
{
    ImVec2 p = ImGui::GetCursorScreenPos();
    ImDrawList* draw_list = ImGui::GetWindowDrawList();

    float height = ImGui::GetFrameHeight();
    float width = height * 1.55f;
    float radius = height * 0.50f;

    ImGui::InvisibleButton(str_id, ImVec2(width, height));
    if (ImGui::IsItemClicked())
        *v = !*v;

    float t = *v ? 1.0f : 0.0f;

    //ImGuiContext& g = *GImGui;
    //float ANIM_SPEED = 0.08f;
    //if (g.LastActiveId == g.CurrentWindow->GetID(str_id))// && g.LastActiveIdTimer < ANIM_SPEED)
    //{
    //    float t_anim = ImSaturate(g.LastActiveIdTimer / ANIM_SPEED);
    //    t = *v ? (t_anim) : (1.0f - t_anim);
    //}

    ImU32 col_bg;
    if (ImGui::IsItemHovered())
        col_bg = ImGui::GetColorU32(ImLerp(ImVec4(0.78f, 0.78f, 0.78f, 1.0f), ImVec4(0.64f, 0.83f, 0.34f, 1.0f), t));
    else
        col_bg = ImGui::GetColorU32(ImLerp(ImVec4(0.85f, 0.85f, 0.85f, 1.0f), ImVec4(0.56f, 0.83f, 0.26f, 1.0f), t));

    draw_list->AddRectFilled(p, ImVec2(p.x + width, p.y + height), col_bg, height * 0.5f);
    draw_list->AddCircleFilled(ImVec2(p.x + radius + t * (width - radius * 2.0f), p.y + radius), radius - 1.5f, IM_COL32(255, 255, 255, 255));
}
#endif

void URenderImGuiNewGameWindow::Paint(FSceneNode* Frame) {
    UpdateUIScale();
    UpdateAnimations();
    
    // Settings popup modal
    if (show_popup) {
        ImGui::OpenPopup("Game Settings");
    }
    
    // Center the modal and make it responsive
    ImVec2 center = ImGui::GetMainViewport()->GetCenter();
    ImVec2 modal_size = ImVec2(
        std::min(GetScaledValue(700.0f), viewport_size.x * 0.95f),
        std::min(GetScaledValue(600.0f), viewport_size.y * 0.95f)
    );
    
    ImGui::SetNextWindowPos(center, ImGuiCond_None, ImVec2(0.5f, 0.5f));
    ImGui::SetNextWindowSize(modal_size, ImGuiCond_None);
    
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, GetScaledValue(12.0f));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(GetScaledValue(20.0f), GetScaledValue(20.0f)));
    
    ImGui::PushStyleColor(ImGuiCol_ModalWindowDimBg, ImVec4(0.7f, 0.7f, 0.7f, 0.0f));
    if (ImGui::BeginPopupModal("Game Settings", &show_popup, ImGuiWindowFlags_NoTitleBar|ImGuiWindowFlags_NoMove|ImGuiWindowFlags_NoResize)) {
        // Header
        ImGui::Text("Game Settings");
        
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.7f, 0.7f, 0.7f, 1.0f));
        ImGui::Text("Customize your game settings before starting a new game.");
        ImGui::PopStyleColor();
        
        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();
        
        RenderMapTypeSection();
        ImGui::Spacing();
        
        RenderDifficultySection();
        ImGui::Spacing();
        
        RenderModifiersSection();
        
        //ImGui::Spacing();
        //ImGui::Separator();
        //ImGui::Spacing();
        
        // Enhanced footer buttons
        float button_width = GetScaledValue(100.0f);
        float button_height = GetScaledValue(35.0f);

        ImVec2 footer_size = ImVec2(button_width * 2 + ImGui::GetStyle().ItemSpacing.x, button_height);
        float footer_height = footer_size.y + ImGui::GetStyle().ItemSpacing.y * 2; // add top and bottom spacing

        // Reserve space at the bottom before drawing rest of the content (if needed)
        // Or place this after drawing the rest of the popup UI

        float content_height = ImGui::GetContentRegionAvail().y;
        if (content_height > footer_height) {
            ImGui::Dummy(ImVec2(0, content_height - footer_height)); // push content upwards
        }

        // Align footer to bottom
        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + ImGui::GetStyle().ItemSpacing.y);

        // Center the buttons horizontally
        float spacing = ImGui::GetStyle().ItemSpacing.x;
        float total_width = button_width * 2 + spacing;
        float offset = (ImGui::GetContentRegionAvail().x - total_width) * 0.5f;
        
        if (offset > 0.0f) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + offset);
        }
        
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, GetScaledValue(6.0f));
        
        // Cancel button
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.5f, 0.5f, 0.5f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
        if (ImGui::Button("Cancel", ImVec2(button_width, button_height))) {
            Close();
        }
        ImGui::PopStyleColor(2);
        
        ImGui::SameLine();
        
        // Start Game button
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.7f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.8f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.1f, 0.6f, 0.2f, 1.0f));
        if (ImGui::Button("Start Game", ImVec2(button_width, button_height))) {
            // Game start logic
            //std::cout << "Starting game with settings:" << std::endl;
            //std::cout << "Difficulty: " << (is_hard_mode ? "Hard" : "Easy") << std::endl;
            //std::cout << "Maps: " << (is_modded_maps ? "Modded" : "Classic") << std::endl;
            
            auto selected_mods = GetSelectedModifiers();

            FString SelectedMutators;

            for (size_t i = 0; i < selected_mods.size(); ++i) {
                SelectedMutators += selected_mods[i].c_str();

                if (i < selected_mods.size() - 1)
                    SelectedMutators += TEXT(",");
            }
            
            Close();

            URenderImGuiNewGameWindow_eventStartGame_Parms Parms;
            Parms.RenewalLevels = is_modded_maps;
            Parms.RenewalGameplay = is_hard_mode;
            Parms.SelectedMutators = SelectedMutators;

            CallEvent(TEXT("StartGame"), &Parms);
        }
        ImGui::PopStyleColor(3);
        ImGui::PopStyleVar();
        
        ImGui::EndPopup();
    }
    ImGui::PopStyleColor();

    ImGui::PopStyleVar(2);
}
