#pragma once
#include "imgui.h"      // IMGUI_IMPL_API
#ifndef IMGUI_DISABLE

// Follow "Getting Started" link and check examples/ folder to learn about using backends!
IMGUI_IMPL_API bool ImGui_ImplUnreal_Init();
IMGUI_IMPL_API void ImGui_ImplUnreal_Shutdown();
IMGUI_IMPL_API void ImGui_ImplUnreal_NewFrame(struct FSceneNode* Frame);
IMGUI_IMPL_API void ImGui_ImplUnreal_RenderDrawData(struct FSceneNode* Frame, ImDrawData* draw_data);

// Called by Init/NewFrame/Shutdown
IMGUI_IMPL_API bool ImGui_ImplUnreal_CreateFontsTexture();
IMGUI_IMPL_API void ImGui_ImplUnreal_DestroyFontsTexture();
IMGUI_IMPL_API bool ImGui_ImplUnreal_CreateDeviceObjects();
IMGUI_IMPL_API void ImGui_ImplUnreal_DestroyDeviceObjects();

#endif // #ifndef IMGUI_DISABLE
