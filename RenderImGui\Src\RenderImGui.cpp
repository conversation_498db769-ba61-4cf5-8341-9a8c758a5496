#include "Engine.h"
#include "UnRender.h"

#pragma pack(push,8)
#include "imgui_impl_unreal.h"
#pragma pack(pop)
#include "RenderImGui.h"

// temp
#if 0
#undef HINSTANCE
#undef HANDLE
#include "windows.h"
#include "imgui_impl_win32.h"
#undef HINSTANCE
#undef HANDLE

extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

WNDPROC originalWndProc = nullptr;  // Store original WndProc

bool IsCapturedEvent(UINT uMsg)
{
    ImGuiIO& io = ImGui::GetIO();
    if (io.WantCaptureMouse)
    {
        switch (uMsg)
        {
        case WM_MOUSEMOVE:
        case WM_MOUSELEAVE:
        case WM_LBUTTONDOWN: 
        case WM_LBUTTONDBLCLK:
        case WM_RBUTTONDOWN: 
        case WM_RBUTTONDBLCLK:
        case WM_MBUTTONDOWN: 
        case WM_MBUTTONDBLCLK:
        case WM_XBUTTONDOWN: 
        case WM_XBUTTONDBLCLK:
        case WM_LBUTTONUP:
        case WM_RBUTTONUP:
        case WM_MBUTTONUP:
        case WM_XBUTTONUP:
        case WM_MOUSEWHEEL:
        case WM_MOUSEHWHEEL:
            return true;
        }

    }

    if (io.WantCaptureKeyboard)
    {
        switch (uMsg)
        {
        case WM_KEYDOWN:
        case WM_KEYUP:
        case WM_SYSKEYDOWN:
        case WM_SYSKEYUP:
            return true;
        }
    }
    return false;
}

LRESULT CALLBACK NewWndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    //if (GFrame->bWindowsMouseAvailable)

    if (IsCapturedEvent(msg) )
        return true;

    // Call the original WndProc to ensure normal message handling
    return CallWindowProc(originalWndProc, hWnd, msg, wParam, lParam);
}

void HookWndProc(HWND hwnd)
{
    if (originalWndProc == nullptr)
    {
        // Retrieve the original WndProc and store it
        originalWndProc = reinterpret_cast<WNDPROC>(GetWindowLongPtr(hwnd, GWLP_WNDPROC));

        // Subclass the window procedure by setting a new one
        SetWindowLongPtr(hwnd, GWLP_WNDPROC, reinterpret_cast<LONG_PTR>(NewWndProc));
    }
}

void UnhookWndProc(HWND hwnd)
{
    if (originalWndProc != nullptr)
    {
        // Restore the original window procedure
        SetWindowLongPtr(hwnd, GWLP_WNDPROC, reinterpret_cast<LONG_PTR>(originalWndProc));
        originalWndProc = nullptr;  // Reset originalWndProc after unhooking
    }
}
#endif

INT URenderImGuiWindow::NumWindows = 0;
INT URenderImGuiWindow::NumDrawn = 0;

void SetupImGuiStyle1()
{
	// DUCK RED nope! is DARK RED style by for40255 from ImThemes
	ImGuiStyle& style = ImGui::GetStyle();
	
	style.Alpha = 1.0f;
	style.DisabledAlpha = 0.6000000238418579f;
	style.WindowPadding = ImVec2(8.0f, 8.0f);
	style.WindowRounding = 0.0f;
	style.WindowBorderSize = 1.0f;
	style.WindowMinSize = ImVec2(32.0f, 32.0f);
	style.WindowTitleAlign = ImVec2(0.5f, 0.5f);
	style.WindowMenuButtonPosition = ImGuiDir_Left;
	style.ChildRounding = 0.0f;
	style.ChildBorderSize = 1.0f;
	style.PopupRounding = 0.0f;
	style.PopupBorderSize = 1.0f;
	style.FramePadding = ImVec2(4.0f, 3.0f);
	style.FrameRounding = 0.0f;
	style.FrameBorderSize = 0.0f;
	style.ItemSpacing = ImVec2(8.0f, 4.0f);
	style.ItemInnerSpacing = ImVec2(4.0f, 4.0f);
	style.CellPadding = ImVec2(4.0f, 2.0f);
	style.IndentSpacing = 21.0f;
	style.ColumnsMinSpacing = 6.0f;
	style.ScrollbarSize = 14.0f;
	style.ScrollbarRounding = 0.0f;
	style.GrabMinSize = 10.0f;
	style.GrabRounding = 0.0f;
	style.TabRounding = 0.0f;
	style.TabBorderSize = 0.0f;
	//style.TabMinWidthForCloseButton = 0.0f;
	style.ColorButtonPosition = ImGuiDir_Right;
	style.ButtonTextAlign = ImVec2(0.5f, 0.5f);
	style.SelectableTextAlign = ImVec2(0.0f, 0.0f);
	
	style.Colors[ImGuiCol_Text] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
	style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.4980392158031464f, 0.4980392158031464f, 0.4980392158031464f, 1.0f);
	style.Colors[ImGuiCol_WindowBg] = ImVec4(0.03921568766236305f, 0.03921568766236305f, 0.03921568766236305f, 1.0f);
	style.Colors[ImGuiCol_ChildBg] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_PopupBg] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_Border] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_FrameBg] = ImVec4(0.1176470592617989f, 0.1176470592617989f, 0.1176470592617989f, 1.0f);
	style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(1.0f, 0.0f, 0.0f, 0.5647059082984924f);
	style.Colors[ImGuiCol_FrameBgActive] = ImVec4(1.0f, 0.0f, 0.0f, 0.5647059082984924f);
	style.Colors[ImGuiCol_TitleBg] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.03921568766236305f, 0.03921568766236305f, 0.03921568766236305f, 1.0f);
	style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
	style.Colors[ImGuiCol_MenuBarBg] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.9411764740943909f);
	style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(1.0f, 0.0f, 0.0f, 0.5647059082984924f);
	style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(1.0f, 0.0f, 0.0f, 0.501960813999176f);
	style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_CheckMark] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_SliderGrab] = ImVec4(1.0f, 0.0f, 0.0f, 0.8154506683349609f);
	style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(1.0f, 0.0f, 0.0f, 0.8156862854957581f);
	style.Colors[ImGuiCol_Button] = ImVec4(1.0f, 0.0f, 0.0f, 0.501960813999176f);
	style.Colors[ImGuiCol_ButtonHovered] = ImVec4(1.0f, 0.0f, 0.0f, 0.7450980544090271f);
	style.Colors[ImGuiCol_ButtonActive] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_Header] = ImVec4(1.0f, 0.0f, 0.0f, 0.6566523313522339f);
	style.Colors[ImGuiCol_HeaderHovered] = ImVec4(1.0f, 0.0f, 0.0f, 0.8039215803146362f);
	style.Colors[ImGuiCol_HeaderActive] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_Separator] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.501960813999176f);
	style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.6695278882980347f);
	style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.9570815563201904f);
	style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.1019607856869698f, 0.1137254908680916f, 0.1294117718935013f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.2039215713739395f, 0.2078431397676468f, 0.2156862765550613f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.3019607961177826f, 0.3019607961177826f, 0.3019607961177826f, 0.2000000029802322f);
	style.Colors[ImGuiCol_Tab] = ImVec4(1.0f, 0.0f, 0.0f, 0.4392156898975372f);
	style.Colors[ImGuiCol_TabHovered] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TabActive] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TabUnfocused] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 0.9725490212440491f);
	style.Colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 1.0f);
	style.Colors[ImGuiCol_PlotLines] = ImVec4(0.6078431606292725f, 0.6078431606292725f, 0.6078431606292725f, 1.0f);
	style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(0.9490196108818054f, 0.3450980484485626f, 0.3450980484485626f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.9490196108818054f, 0.3450980484485626f, 0.3450980484485626f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(0.4274509847164154f, 0.3607843220233917f, 0.3607843220233917f, 1.0f);
	style.Colors[ImGuiCol_TableHeaderBg] = ImVec4(1.0f, 0.0f, 0.0f, 0.7124463319778442f);
	style.Colors[ImGuiCol_TableBorderStrong] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TableBorderLight] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TableRowBg] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TableRowBgAlt] = ImVec4(0.196078434586525f, 0.196078434586525f, 0.196078434586525f, 0.6274510025978088f);
	style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_DragDropTarget] = ImVec4(0.2588235437870026f, 0.2705882489681244f, 0.3803921639919281f, 1.0f);
	style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.1803921610116959f, 0.2274509817361832f, 0.2784313857555389f, 1.0f);
	style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.0f, 1.0f, 1.0f, 0.699999988079071f);
	style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.3499999940395355f);
}

void SetupImGuiStyle2()
{
	// Darky style by 90th from ImThemes
	ImGuiStyle& style = ImGui::GetStyle();
	
	style.Alpha = 1.0f;
	style.DisabledAlpha = 0.6000000238418579f;
	style.WindowPadding = ImVec2(8.0f, 8.0f);
	style.WindowRounding = 0.0f;
	style.WindowBorderSize = 1.0f;
	style.WindowMinSize = ImVec2(32.0f, 32.0f);
	style.WindowTitleAlign = ImVec2(0.0f, 0.5f);
	style.WindowMenuButtonPosition = ImGuiDir_Left;
	style.ChildRounding = 0.0f;
	style.ChildBorderSize = 1.0f;
	style.PopupRounding = 0.0f;
	style.PopupBorderSize = 1.0f;
	style.FramePadding = ImVec2(4.0f, 3.0f);
	style.FrameRounding = 0.0f;
	style.FrameBorderSize = 0.0f;
	style.ItemSpacing = ImVec2(8.0f, 4.0f);
	style.ItemInnerSpacing = ImVec2(4.0f, 4.0f);
	style.CellPadding = ImVec2(4.0f, 2.0f);
	style.IndentSpacing = 21.0f;
	style.ColumnsMinSpacing = 6.0f;
	style.ScrollbarSize = 14.0f;
	style.ScrollbarRounding = 0.0f;
	style.GrabMinSize = 10.0f;
	style.GrabRounding = 0.0f;
	style.TabRounding = 0.0f;
	style.TabBorderSize = 0.0f;
	//style.TabMinWidthForCloseButton = 0.0f;
	style.ColorButtonPosition = ImGuiDir_Right;
	style.ButtonTextAlign = ImVec2(0.5f, 0.5f);
	style.SelectableTextAlign = ImVec2(0.0f, 0.0f);
	
	style.Colors[ImGuiCol_Text] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
	style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.4980392158031464f, 0.4980392158031464f, 0.4980392158031464f, 1.0f);
	style.Colors[ImGuiCol_WindowBg] = ImVec4(0.03921568766236305f, 0.03921568766236305f, 0.03921568766236305f, 1.0f);
	style.Colors[ImGuiCol_ChildBg] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_PopupBg] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.8583691120147705f);
	style.Colors[ImGuiCol_Border] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.0f, 0.0f, 0.0f, 0.6995707750320435f);
	style.Colors[ImGuiCol_FrameBg] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 1.0f);
	style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f);
	style.Colors[ImGuiCol_TitleBg] = ImVec4(0.1843137294054031f, 0.1921568661928177f, 0.2117647081613541f, 1.0f);
	style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.03921568766236305f, 0.03921568766236305f, 0.03921568766236305f, 1.0f);
	style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_MenuBarBg] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.9399999976158142f);
	style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.01960784383118153f, 0.01960784383118153f, 0.01960784383118153f, 0.5299999713897705f);
	style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.3098039329051971f, 0.3098039329051971f, 0.3098039329051971f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.407843142747879f, 0.407843142747879f, 0.407843142747879f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.5098039507865906f, 0.5098039507865906f, 0.5098039507865906f, 1.0f);
	style.Colors[ImGuiCol_CheckMark] = ImVec4(0.6470588445663452f, 0.2313725501298904f, 0.2313725501298904f, 1.0f);
	style.Colors[ImGuiCol_SliderGrab] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
	style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
	style.Colors[ImGuiCol_Button] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 1.0f);
	style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 1.0f);
	style.Colors[ImGuiCol_Header] = ImVec4(0.05490196123719215f, 0.05490196123719215f, 0.05490196123719215f, 1.0f);
	style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 1.0f);
	style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 1.0f);
	style.Colors[ImGuiCol_Separator] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.501960813999176f);
	style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.6695278882980347f);
	style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.9570815563201904f);
	style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.1019607856869698f, 0.1137254908680916f, 0.1294117718935013f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.2039215713739395f, 0.2078431397676468f, 0.2156862765550613f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.3019607961177826f, 0.3019607961177826f, 0.3019607961177826f, 0.2000000029802322f);
	style.Colors[ImGuiCol_Tab] = ImVec4(0.1843137294054031f, 0.1921568661928177f, 0.2117647081613541f, 1.0f);
	style.Colors[ImGuiCol_TabHovered] = ImVec4(0.2352941185235977f, 0.2470588237047195f, 0.2705882489681244f, 1.0f);
	style.Colors[ImGuiCol_TabActive] = ImVec4(0.2588235437870026f, 0.2745098173618317f, 0.3019607961177826f, 1.0f);
	style.Colors[ImGuiCol_TabUnfocused] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 0.9725490212440491f);
	style.Colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.06666667014360428f, 0.06666667014360428f, 0.06666667014360428f, 1.0f);
	style.Colors[ImGuiCol_PlotLines] = ImVec4(0.6078431606292725f, 0.6078431606292725f, 0.6078431606292725f, 1.0f);
	style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(0.9490196108818054f, 0.3450980484485626f, 0.3450980484485626f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.9490196108818054f, 0.3450980484485626f, 0.3450980484485626f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(0.4274509847164154f, 0.3607843220233917f, 0.3607843220233917f, 1.0f);
	style.Colors[ImGuiCol_TableHeaderBg] = ImVec4(0.1882352977991104f, 0.1882352977991104f, 0.2000000029802322f, 1.0f);
	style.Colors[ImGuiCol_TableBorderStrong] = ImVec4(0.3098039329051971f, 0.3098039329051971f, 0.3490196168422699f, 1.0f);
	style.Colors[ImGuiCol_TableBorderLight] = ImVec4(0.2274509817361832f, 0.2274509817361832f, 0.2470588237047195f, 1.0f);
	style.Colors[ImGuiCol_TableRowBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
	style.Colors[ImGuiCol_TableRowBgAlt] = ImVec4(1.0f, 1.0f, 1.0f, 0.05999999865889549f);
	style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(1.0f, 0.8784313797950745f, 0.8784313797950745f, 1.0f);
	style.Colors[ImGuiCol_DragDropTarget] = ImVec4(0.2588235437870026f, 0.2705882489681244f, 0.3803921639919281f, 1.0f);
	style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.1803921610116959f, 0.2274509817361832f, 0.2784313857555389f, 1.0f);
	style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.0f, 1.0f, 1.0f, 0.699999988079071f);
	style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.3499999940395355f);
}

void SetupImGuiStyleGold()
{
	// Gold style by CookiePLMonster from ImThemes
	ImGuiStyle& style = ImGui::GetStyle();
	
	style.Alpha = 1.0f;
	style.DisabledAlpha = 0.6000000238418579f;
	style.WindowPadding = ImVec2(8.0f, 8.0f);
	style.WindowRounding = 4.0f;
	style.WindowBorderSize = 1.0f;
	style.WindowMinSize = ImVec2(32.0f, 32.0f);
	style.WindowTitleAlign = ImVec2(1.0f, 0.5f);
	style.WindowMenuButtonPosition = ImGuiDir_Right;
	style.ChildRounding = 0.0f;
	style.ChildBorderSize = 1.0f;
	style.PopupRounding = 4.0f;
	style.PopupBorderSize = 1.0f;
	style.FramePadding = ImVec2(4.0f, 2.0f);
	style.FrameRounding = 4.0f;
	style.FrameBorderSize = 0.0f;
	style.ItemSpacing = ImVec2(10.0f, 2.0f);
	style.ItemInnerSpacing = ImVec2(4.0f, 4.0f);
	style.CellPadding = ImVec2(4.0f, 2.0f);
	style.IndentSpacing = 12.0f;
	style.ColumnsMinSpacing = 6.0f;
	style.ScrollbarSize = 10.0f;
	style.ScrollbarRounding = 6.0f;
	style.GrabMinSize = 10.0f;
	style.GrabRounding = 4.0f;
	style.TabRounding = 4.0f;
	style.TabBorderSize = 0.0f;
	//style.TabMinWidthForCloseButton = 0.0f;
	style.ColorButtonPosition = ImGuiDir_Right;
	style.ButtonTextAlign = ImVec2(0.5f, 0.5f);
	style.SelectableTextAlign = ImVec2(0.0f, 0.0f);
	
	style.Colors[ImGuiCol_Text] = ImVec4(0.9176470637321472f, 0.9176470637321472f, 0.9176470637321472f, 1.0f);
	style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.4392156898975372f, 0.4392156898975372f, 0.4392156898975372f, 1.0f);
	style.Colors[ImGuiCol_WindowBg] = ImVec4(0.05882352963089943f, 0.05882352963089943f, 0.05882352963089943f, 1.0f);
	style.Colors[ImGuiCol_ChildBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
	style.Colors[ImGuiCol_PopupBg] = ImVec4(0.0784313753247261f, 0.0784313753247261f, 0.0784313753247261f, 0.9399999976158142f);
	style.Colors[ImGuiCol_Border] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
	style.Colors[ImGuiCol_FrameBg] = ImVec4(0.1098039224743843f, 0.1098039224743843f, 0.1098039224743843f, 1.0f);
	style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_TitleBg] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.0f, 0.0f, 0.0f, 0.5099999904632568f);
	style.Colors[ImGuiCol_MenuBarBg] = ImVec4(0.1098039224743843f, 0.1098039224743843f, 0.1098039224743843f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.05882352963089943f, 0.05882352963089943f, 0.05882352963089943f, 0.5299999713897705f);
	style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.2078431397676468f, 0.2078431397676468f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.4666666686534882f, 0.4666666686534882f, 0.4666666686534882f, 1.0f);
	style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.8078431487083435f, 0.8274509906768799f, 0.8078431487083435f, 1.0f);
	style.Colors[ImGuiCol_CheckMark] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_Button] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_Header] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.929411768913269f, 0.6470588445663452f, 0.1372549086809158f, 1.0f);
	style.Colors[ImGuiCol_Separator] = ImVec4(0.2078431397676468f, 0.2078431397676468f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.2078431397676468f, 0.2078431397676468f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_Tab] = ImVec4(0.5098039507865906f, 0.3568627536296844f, 0.1490196138620377f, 1.0f);
	style.Colors[ImGuiCol_TabHovered] = ImVec4(0.9098039269447327f, 0.6392157077789307f, 0.1294117718935013f, 1.0f);
	style.Colors[ImGuiCol_TabActive] = ImVec4(0.7764706015586853f, 0.5490196347236633f, 0.2078431397676468f, 1.0f);
	style.Colors[ImGuiCol_TabUnfocused] = ImVec4(0.06666667014360428f, 0.09803921729326248f, 0.1490196138620377f, 0.9700000286102295f);
	style.Colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.1372549086809158f, 0.2588235437870026f, 0.4196078479290009f, 1.0f);
	style.Colors[ImGuiCol_PlotLines] = ImVec4(0.6078431606292725f, 0.6078431606292725f, 0.6078431606292725f, 1.0f);
	style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.0f, 0.4274509847164154f, 0.3490196168422699f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.8980392217636108f, 0.6980392336845398f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.0f, 0.6000000238418579f, 0.0f, 1.0f);
	style.Colors[ImGuiCol_TableHeaderBg] = ImVec4(0.1882352977991104f, 0.1882352977991104f, 0.2000000029802322f, 1.0f);
	style.Colors[ImGuiCol_TableBorderStrong] = ImVec4(0.3098039329051971f, 0.3098039329051971f, 0.3490196168422699f, 1.0f);
	style.Colors[ImGuiCol_TableBorderLight] = ImVec4(0.2274509817361832f, 0.2274509817361832f, 0.2470588237047195f, 1.0f);
	style.Colors[ImGuiCol_TableRowBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
	style.Colors[ImGuiCol_TableRowBgAlt] = ImVec4(1.0f, 1.0f, 1.0f, 0.05999999865889549f);
	style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(0.2588235437870026f, 0.5882353186607361f, 0.9764705896377563f, 0.3499999940395355f);
	style.Colors[ImGuiCol_DragDropTarget] = ImVec4(1.0f, 1.0f, 0.0f, 0.8999999761581421f);
	style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.2588235437870026f, 0.5882353186607361f, 0.9764705896377563f, 1.0f);
	style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.0f, 1.0f, 1.0f, 0.699999988079071f);
	style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.2000000029802322f);
	style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.800000011920929f, 0.800000011920929f, 0.800000011920929f, 0.3499999940395355f);
}

URenderImGuiWindow::URenderImGuiWindow()
{
    OwnerWindow = NULL;
    EventHandler = NULL;
    GFrame = NULL;

    if( NumWindows == 0 )
    {
        // Setup Dear ImGui context
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO(); (void)io;
        io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;   // Enable Keyboard Controls
        io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;    // Enable Gamepad Controls

        // Setup Dear ImGui style
        //ImGui::StyleColorsDark();
        //ImGui::StyleColorsClassic();
        SetupImGuiStyleGold();

        // Setup Platform/Renderer backends
        ImGui_ImplUnreal_Init();
    }

    NumWindows++;
}

void URenderImGuiWindow::Destroy()
{
    NumWindows--;

    if( NumWindows == 0 )
    {
        // Cleanup
        ImGui_ImplUnreal_Shutdown();
        ImGui::DestroyContext();
    }

    Super::Destroy();
}

// example window
void URenderImGuiWindow::Paint(FSceneNode* Frame)
{
    ImGui::SetNextWindowPos(ImVec2(WinLeft, WinTop), ImGuiCond_Once); // ImGuiCond_FirstUseEver
    ImGui::SetNextWindowSize(ImVec2(WinWidth, WinHeight), ImGuiCond_Once);

    if( !ImGui::Begin(*MakeUniqueName(*WindowTitle)) ) // ImGuiWindowFlags_NoTitleBar
    {
        ImGui::End();
        return;
    }

    ImVec2 mousePositionAbsolute = ImGui::GetMousePos();
    ImGui::Text("Mouse Position: %f, %f", mousePositionAbsolute.x, mousePositionAbsolute.y);
    ImGui::Text("Mouse clicked: %s", ImGui::IsMouseDown(ImGuiMouseButton_Left) ? "Yes" : "No");

    ImGui::End();
}

void URenderImGuiWindow::Show()
{
    if( OwnerWindow )
    {
        UFunction* Func = OwnerWindow->FindFunction(TEXT("Show"));
        if( Func )
            OwnerWindow->ProcessEvent(Func, NULL);
    }

    Shown();
}

void URenderImGuiWindow::Close()
{
    if( OwnerWindow )
    {
        UBOOL ByParent = 0;

        UFunction* Func = OwnerWindow->FindFunction(TEXT("Close"));
        if( Func )
            OwnerWindow->ProcessEvent(Func, &ByParent);
    }

    Closed();
}

void URenderImGuiWindow::Hide()
{
    if( OwnerWindow )
    {
        UFunction* Func = OwnerWindow->FindFunction(TEXT("Hide"));
        if( Func )
            OwnerWindow->ProcessEvent(Func, NULL);
    }

    Hidden();
}

void URenderImGuiWindow::CallEvent(const TCHAR* EventName, void* Parms)
{
    if( EventHandler )
    {
        UFunction* Func = EventHandler->FindFunction(EventName);
        if( Func )
            EventHandler->ProcessEvent(Func, Parms);
    }
}

void URenderImGuiWindow::execCreateImGuiWindow(FFrame& Stack, RESULT_DECL)
{
    P_GET_OBJECT(UCanvas,Canvas);
    P_GET_FLOAT(X);
    P_GET_FLOAT(Y);
    P_GET_FLOAT(XL);
    P_GET_FLOAT(YL);
    P_FINISH;

}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execCreateImGuiWindow);

void URenderImGuiWindow::execDestroyImGuiWindow(FFrame& Stack, RESULT_DECL)
{
    P_GET_OBJECT(UCanvas,Canvas);
    P_GET_FLOAT(X);
    P_GET_FLOAT(Y);
    P_GET_FLOAT(XL);
    P_GET_FLOAT(YL);
    P_FINISH;

}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execDestroyImGuiWindow);

void URenderImGuiWindow::execImGuiWindowShown(FFrame& Stack, RESULT_DECL)
{
    P_FINISH;

    Shown();
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execImGuiWindowShown);

void URenderImGuiWindow::execImGuiWindowHidden(FFrame& Stack, RESULT_DECL)
{
    P_FINISH;

    Hidden();
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execImGuiWindowHidden);

void URenderImGuiWindow::execImGuiWindowClosed(FFrame& Stack, RESULT_DECL)
{
    P_FINISH;

    Closed();
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execImGuiWindowClosed);

void URenderImGuiWindow::execImGuiWindowResized(FFrame& Stack, RESULT_DECL)
{
    P_FINISH;

    Resized();
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execImGuiWindowResized);

void URenderImGuiWindow::execPaintImGuiWindow(FFrame& Stack, RESULT_DECL)
{
    P_GET_OBJECT(UCanvas,Canvas);
    P_GET_STR(WindowName);
    P_FINISH;

    if( NumDrawn++ == 0 )
    {
        ImGuiIO& io = ImGui::GetIO();

        bWantCaptureMouse = io.WantCaptureMouse;
        bWantCaptureKeyboard = io.WantCaptureKeyboard;

        io.MousePos.x = MouseX;
        io.MousePos.y = MouseY;
        //io.AddMousePosEvent(MouseX, MouseY);

        // Start the Dear ImGui frame
        ImGui_ImplUnreal_NewFrame(Canvas->Frame);
        ImGui::NewFrame();
    }

    //ImVec2 WindowPos = ImGui::GetWindowPos();
    //WinLeft = WindowPos.x;
    //WinTop = WindowPos.y;
    //
    //ImVec2 WindowSize = ImGui::GetWindowSize();
    //WinWidth = WindowSize.x;
    //WinHeight = WindowSize.y;
       
    GFrame = Canvas->Frame;
    Paint(Canvas->Frame);

    if( NumWindows == NumDrawn )
    {
        NumDrawn = 0;

        Canvas->Frame->Viewport->RenDev->ClearZ(Canvas->Frame);

        // Rendering
        //ImGui::EndFrame();
        ImGui::Render();
        ImGui_ImplUnreal_RenderDrawData(Canvas->Frame, ImGui::GetDrawData());
    }
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execPaintImGuiWindow);

void URenderImGuiWindow::execMouseMove(FFrame& Stack, RESULT_DECL)
{
    P_GET_FLOAT(X);
    P_GET_FLOAT(Y);
    P_FINISH;

    ImGuiIO& io = ImGui::GetIO();

    //ImVec2 Rect = ImGui::GetItemRectMin();

    io.AddMousePosEvent(X, Y);  // update mouse position
    //io.AddMouseButtonEvent(ImGuiMouseButton_Left, Frame->Viewport->Input->KeyDown(IK_LeftMouse));  // update mouse button states
    //io.AddMouseButtonEvent(ImGuiMouseButton_Right, Frame->Viewport->Input->KeyDown(IK_RightMouse));  // update mouse button states
    //io.AddMouseButtonEvent(ImGuiMouseButton_Middle, Frame->Viewport->Input->KeyDown(IK_MiddleMouse));  // update mouse button states
    //AddMouseWheelEvent
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execMouseMove);

void URenderImGuiWindow::execMouseClick(FFrame& Stack, RESULT_DECL)
{
    P_GET_INT(button); // ImGuiMouseButton_Left
    P_GET_UBOOL(down);
    P_FINISH;

    ImGuiIO& io = ImGui::GetIO();
    
    io.AddMouseButtonEvent(button, down);  // update mouse button states
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execMouseClick);

void URenderImGuiWindow::execAddInputCharacter(FFrame& Stack, RESULT_DECL)
{
    P_GET_BYTE(c);
    P_FINISH;

    ImGuiIO& io = ImGui::GetIO();
    io.AddInputCharacter(c);
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execAddInputCharacter);

ImGuiKey InputKeyToImGuiKey(INT key)
{
    switch (key)
    {
    case IK_Tab: return ImGuiKey_Tab;
    case IK_Left: return ImGuiKey_LeftArrow;
    case IK_Right: return ImGuiKey_RightArrow;
    case IK_Up: return ImGuiKey_UpArrow;
    case IK_Down: return ImGuiKey_DownArrow;
    case IK_PageUp: return ImGuiKey_PageUp;
    case IK_PageDown: return ImGuiKey_PageDown;
    case IK_Home: return ImGuiKey_Home;
    case IK_End: return ImGuiKey_End;
    case IK_Insert: return ImGuiKey_Insert;
    case IK_Delete: return ImGuiKey_Delete;
    case IK_Backspace: return ImGuiKey_Backspace;
    case IK_Space: return ImGuiKey_Space;
    case IK_Enter: return ImGuiKey_Enter;
    case IK_Escape: return ImGuiKey_Escape;
    //case VK_OEM_7: return ImGuiKey_Apostrophe;
    case IK_Comma: return ImGuiKey_Comma;
    //case VK_OEM_MINUS: return ImGuiKey_Minus;
    case IK_Period: return ImGuiKey_Period;
    //case VK_OEM_2: return ImGuiKey_Slash;
    //case VK_OEM_1: return ImGuiKey_Semicolon;
    //case VK_OEM_PLUS: return ImGuiKey_Equal;
    //case VK_OEM_4: return ImGuiKey_LeftBracket;
    //case VK_OEM_5: return ImGuiKey_Backslash;
    //case VK_OEM_6: return ImGuiKey_RightBracket;
    //case VK_OEM_3: return ImGuiKey_GraveAccent;
    case IK_CapsLock: return ImGuiKey_CapsLock;
    case IK_ScrollLock: return ImGuiKey_ScrollLock;
    case IK_NumLock: return ImGuiKey_NumLock;
    case IK_PrintScrn: return ImGuiKey_PrintScreen;
    case IK_Pause: return ImGuiKey_Pause;
    case IK_NumPad0: return ImGuiKey_Keypad0;
    case IK_NumPad1: return ImGuiKey_Keypad1;
    case IK_NumPad2: return ImGuiKey_Keypad2;
    case IK_NumPad3: return ImGuiKey_Keypad3;
    case IK_NumPad4: return ImGuiKey_Keypad4;
    case IK_NumPad5: return ImGuiKey_Keypad5;
    case IK_NumPad6: return ImGuiKey_Keypad6;
    case IK_NumPad7: return ImGuiKey_Keypad7;
    case IK_NumPad8: return ImGuiKey_Keypad8;
    case IK_NumPad9: return ImGuiKey_Keypad9;
    case IK_NumPadPeriod: return ImGuiKey_KeypadDecimal;
    case IK_GreySlash: return ImGuiKey_KeypadDivide;
    case IK_GreyStar: return ImGuiKey_KeypadMultiply;
    case IK_GreyMinus: return ImGuiKey_KeypadSubtract;
    case IK_GreyPlus: return ImGuiKey_KeypadAdd;
    case IK_Shift: return ImGuiKey_LeftShift;
    case IK_LShift: return ImGuiKey_LeftShift;
    case IK_Ctrl: return ImGuiKey_LeftCtrl;
    case IK_LControl: return ImGuiKey_LeftCtrl;
    case IK_Alt: return ImGuiKey_LeftAlt;
    case IK_UnknownA4: return ImGuiKey_LeftAlt;
    case IK_Unknown5B: return ImGuiKey_LeftSuper;
    case IK_RShift: return ImGuiKey_RightShift;
    case IK_RControl: return ImGuiKey_RightCtrl;
    case IK_UnknownA5: return ImGuiKey_RightAlt;
    case IK_Unknown5C: return ImGuiKey_RightSuper;
    case IK_Unknown5D: return ImGuiKey_Menu;
    case IK_F1: return ImGuiKey_F1;
    case IK_F2: return ImGuiKey_F2;
    case IK_F3: return ImGuiKey_F3;
    case IK_F4: return ImGuiKey_F4;
    case IK_F5: return ImGuiKey_F5;
    case IK_F6: return ImGuiKey_F6;
    case IK_F7: return ImGuiKey_F7;
    case IK_F8: return ImGuiKey_F8;
    case IK_F9: return ImGuiKey_F9;
    case IK_F10: return ImGuiKey_F10;
    case IK_F11: return ImGuiKey_F11;
    case IK_F12: return ImGuiKey_F12;
    case IK_F13: return ImGuiKey_F13;
    case IK_F14: return ImGuiKey_F14;
    case IK_F15: return ImGuiKey_F15;
    case IK_F16: return ImGuiKey_F16;
    case IK_F17: return ImGuiKey_F17;
    case IK_F18: return ImGuiKey_F18;
    case IK_F19: return ImGuiKey_F19;
    case IK_F20: return ImGuiKey_F20;
    case IK_F21: return ImGuiKey_F21;
    case IK_F22: return ImGuiKey_F22;
    case IK_F23: return ImGuiKey_F23;
    case IK_F24: return ImGuiKey_F24;
    case IK_UnknownA6: return ImGuiKey_AppBack;
    case IK_UnknownA7: return ImGuiKey_AppForward;
    }

    if( key >= ImGuiKey_NamedKey_BEGIN && key < ImGuiKey_NamedKey_END )
        return (ImGuiKey)key;

    return ImGuiKey_None;
}

void URenderImGuiWindow::execAddKeyEvent(FFrame& Stack, RESULT_DECL)
{
    P_GET_INT(button);
    P_GET_UBOOL(down);
    P_FINISH;

    ImGuiIO& io = ImGui::GetIO();

    // special case
    if( button == IK_MWheelUp || button == IK_MWheelDown )
    {
        io.AddMouseWheelEvent(0.0, button == IK_MWheelUp ? 1.0 : -1.0);
        return;
    }

    ImGuiKey key = InputKeyToImGuiKey(button);
    if( key != ImGuiKey_None )
        io.AddKeyEvent(key, down);
}
IMPLEMENT_FUNCTION(URenderImGuiWindow, INDEX_NONE, execAddKeyEvent);

IMPLEMENT_CLASS(URenderImGuiWindow)

IMPLEMENT_PACKAGE(RenderImGui)
