# ULevelExporterT3D_UE5 Usage Guide

## Overview

The `ULevelExporterT3D_UE5` class is a new level exporter that exports Unreal Engine levels to the T3D format compatible with Unreal Engine 5. This exporter extends the functionality of the original `ULevelExporterT3D` class with enhanced features and UE5-specific metadata.

## Features

### Enhanced T3D Format Support
- **UE5 Compatibility**: Exports levels in a format that can be imported into Unreal Engine 5
- **Component-Based Architecture**: Supports UE5's component-based actor system
- **Enhanced Metadata**: Includes detailed actor metadata for better import fidelity
- **Modern Coordinate System**: Proper handling of UE5's coordinate system and transforms
- **Actor Tags**: Exports UE5-style actor tags for better organization
- **Static Mesh Support**: Enhanced static mesh actor export with component information

### Key Differences from Original T3D Exporter

1. **File Format**: Uses "T3D_UE5" and "UE5" file extensions
2. **Enhanced Actor Export**: Each actor includes:
   - Unique GUID for tracking
   - Sanitized actor labels
   - Component hierarchy information
   - Enhanced metadata
   - Actor tags for organization

3. **Level Settings**: Exports UE5-specific level settings including:
   - World settings (gravity, physics, game mode)
   - Lighting settings (static lighting, indirect lighting)
   - Lightmap configuration

4. **Component Information**: Exports actor components including:
   - Root component (SceneComponent)
   - Static mesh components
   - Component transforms and properties

## Usage

### Basic Export

```cpp
// Get the level to export
ULevel* Level = GetCurrentLevel();

// Create the UE5 exporter
ULevelExporterT3D_UE5* Exporter = new ULevelExporterT3D_UE5();

// Export to file
FString Filename = TEXT("MyLevel.T3D_UE5");
UExporter::ExportToFile(Level, Exporter, *Filename);
```

### Export Selected Actors Only

```cpp
// Export only selected actors (similar to COPY format)
UExporter::ExportToFile(Level, Exporter, *Filename, 0, 0);
// Use "COPY" as the type parameter to export only selected actors
```

### Programmatic Export

```cpp
// Export to a string buffer
FStringOutputDevice OutputBuffer;
Exporter->ExportText(Level, TEXT("T3D_UE5"), OutputBuffer, GWarn);
FString ExportedData = OutputBuffer;
```

## Output Format

The exported T3D_UE5 file contains the following sections:

### 1. Map Header
```
Begin Map Name="LevelName" Version="UE5"
```

### 2. Level Settings
```
Begin LevelSettings
    WorldSettings=(...)
    LightingSettings=(...)
End LevelSettings
```

### 3. Lighting Information
```
Begin LightingInfo
    Light_1=(...)
    LightmapSettings=(...)
End LightingInfo
```

### 4. Static Mesh Actors Summary
```
Begin StaticMeshActors
    StaticMeshActor_1=(...)
End StaticMeshActors
```

### 5. Detailed Actor Information
```
Begin Actors
    Begin Actor Class=StaticMeshActor Name=MyActor
        ActorLabel="MyActor"
        ActorGuid="StaticMeshActor_MyActor_12345"
        ActorMetaData=(...)
        ActorTags=(...)
        RootComponent=(...)
        StaticMeshComponent=(...)
    End Actor
End Actors
```

### 6. Surface Information
```
Begin Surface
    Material="..."
    BasePoint=...
    TextureU=...
    TextureV=...
    TexurePan=...
    Normal=...
    PolyFlags=...
End Surface
```

## Implementation Details

### Helper Functions

- `SanitizeActorName()`: Cleans actor names for UE5 compatibility
- `GetActorUniqueId()`: Generates unique identifiers for actors
- `ExportActorUE5()`: Exports individual actors with UE5 enhancements
- `ExportActorComponents()`: Exports actor component hierarchy
- `ExportActorMetadata()`: Exports detailed actor metadata
- `ExportActorTags()`: Exports UE5-style actor tags
- `ExportLevelSettings()`: Exports level configuration
- `ExportStaticMeshActors()`: Exports static mesh actor summary
- `ExportLightingInfo()`: Exports lighting and lightmap information

### Coordinate System

The exporter handles coordinate system conversion for UE5 compatibility:
- Converts rotation values from Unreal units to degrees
- Maintains proper transform hierarchy
- Ensures component-relative transforms are correct

### Actor Filtering

The exporter respects the same filtering as the original:
- Excludes camera actors from export
- Supports "AllSelected" vs "Selected Only" modes
- Maintains compatibility with existing export workflows

## Integration

The `ULevelExporterT3D_UE5` class is integrated into the Editor module and can be used anywhere the original `ULevelExporterT3D` is used. It follows the same UExporter interface pattern and can be discovered automatically by the export system.

## File Extensions

- `.T3D_UE5`: Primary UE5 T3D format
- `.UE5`: Alternative UE5 format extension

Both extensions are registered with the exporter and can be used interchangeably.
