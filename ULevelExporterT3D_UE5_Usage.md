# ULevelExporterT3D_UE5 Usage Guide

## Overview

The `ULevelExporterT3D_UE5` class is a new level exporter that exports Unreal Engine levels to the T3D format compatible with Unreal Engine 5. This exporter follows the exact UE5 T3D format specification based on the official UE5 EditorExporters.cpp implementation.

## Features

### Authentic UE5 T3D Format Support
- **UE5 Compatibility**: Exports levels in the exact format used by Unreal Engine 5
- **Proper Structure**: Follows UE5's Begin Map → Begin Level → Actors → End Level → Begin Surface → End Surface → End Map structure
- **Enhanced Actor Metadata**: Includes all UE5 actor attributes like Archetype, ParentActor, SocketName, GroupActor, GroupFolder, ActorFolderPath, CopyPasteId, ContentBundleGuid, ExternalDataLayer, and ExportPath
- **Standard Property Export**: Uses the same property export system as the original exporter for maximum compatibility
- **Surface Information**: Exports BSP surface data in UE5 format with TEXTURE, BASE, TEXTUREU, TEXTUREV, NORMAL, POLYFLAGS

### Key Differences from Original T3D Exporter

1. **File Format**: Uses "T3D_UE5" and "UE5" file extensions
2. **Enhanced Actor Export**: Each actor includes the full UE5 actor header with:
   - Class path (full path name)
   - Actor name
   - Archetype reference
   - Parent actor information
   - Socket name for attachments
   - Group actor and folder information
   - Actor folder path
   - Copy/paste ID
   - Content bundle GUID
   - External data layer asset
   - Export path for proper referencing

3. **Proper Level Structure**: Follows UE5's exact level export structure
4. **Surface Format**: Uses UE5's surface export format with proper field names

## Usage

### Basic Export

```cpp
// Get the level to export
ULevel* Level = GetCurrentLevel();

// Create the UE5 exporter
ULevelExporterT3D_UE5* Exporter = new ULevelExporterT3D_UE5();

// Export to file
FString Filename = TEXT("MyLevel.T3D_UE5");
UExporter::ExportToFile(Level, Exporter, *Filename);
```

### Export Selected Actors Only

```cpp
// Export only selected actors (similar to COPY format)
UExporter::ExportToFile(Level, Exporter, *Filename, 0, 0);
// Use "COPY" as the type parameter to export only selected actors
```

### Programmatic Export

```cpp
// Export to a string buffer
FStringOutputDevice OutputBuffer;
Exporter->ExportText(Level, TEXT("T3D_UE5"), OutputBuffer, GWarn);
FString ExportedData = OutputBuffer;
```

## Output Format

The exported T3D_UE5 file follows the exact UE5 format structure:

### 1. Map Header
```
Begin Map Name="PackageName"
```

### 2. Level Section
```
Begin Level NAME="LevelName"
```

### 3. Actor Information (UE5 Format)
```
Begin Actor Class=/Script/Engine.StaticMeshActor Name=MyActor Archetype=StaticMeshActor'/Script/Engine.StaticMeshActor' ExportPath=StaticMeshActor'Level:MyActor'
    Location=(X=100.000000,Y=200.000000,Z=300.000000)
    Rotation=(Pitch=0.000000,Yaw=45.000000,Roll=0.000000)
    DrawScale=1.000000
    Mesh=StaticMesh'MyPackage.MyMesh'
    [... all other actor properties exported via standard property system ...]
End Actor
```

### 4. Level End
```
End Level
```

### 5. Surface Information (UE5 Format)
```
Begin Surface
   TEXTURE=Material'/Game/Materials/MyMaterial'
   BASE      X=100.000000,Y=200.000000,Z=300.000000
   TEXTUREU  X=1.000000,Y=0.000000,Z=0.000000
   TEXTUREV  X=0.000000,Y=1.000000,Z=0.000000
   NORMAL    X=0.000000,Y=0.000000,Z=1.000000
   POLYFLAGS=0
End Surface
```

### 6. Map End
```
End Map
```

## Implementation Details

### Core Functions

- `StaticConstructor()`: Registers the exporter with T3D_UE5 and UE5 file extensions
- `ExportText()`: Main export function that follows UE5's exact export structure
- `ExportActorUE5()`: Exports individual actors with full UE5 actor header format

### UE5 Actor Header Format

The exporter generates the complete UE5 actor header including:
- **Class**: Full class path name (e.g., `/Script/Engine.StaticMeshActor`)
- **Name**: Actor instance name
- **Archetype**: Reference to the actor's archetype
- **ParentActor**: Parent actor for attachment (empty in UE1)
- **SocketName**: Socket name for attachment (empty in UE1)
- **GroupActor**: Group actor reference (empty in UE1)
- **GroupFolder**: Group folder path (empty in UE1)
- **ActorFolderPath**: Actor folder organization (empty in UE1)
- **CopyPasteId**: Copy/paste identifier (empty in UE1)
- **ContentBundleGuid**: Content bundle GUID (empty in UE1)
- **ExternalDataLayer**: External data layer asset (empty in UE1)
- **ExportPath**: Full export path for proper object referencing

### Property Export

Uses the standard `ExportProperties()` function to export all actor properties, ensuring:
- Complete property coverage
- Proper default value handling
- Correct property formatting
- Maximum compatibility with UE5 import

### Actor Filtering

The exporter respects the same filtering as the original:
- Excludes camera actors from export
- Supports "AllSelected" vs "Selected Only" modes
- Maintains compatibility with existing export workflows
- Handles COPY vs full export modes properly

## Integration

The `ULevelExporterT3D_UE5` class is integrated into the Editor module and can be used anywhere the original `ULevelExporterT3D` is used. It follows the same UExporter interface pattern and can be discovered automatically by the export system.

## File Extensions

- `.T3D_UE5`: Primary UE5 T3D format
- `.UE5`: Alternative UE5 format extension

Both extensions are registered with the exporter and can be used interchangeably.
